from typing import Optional, Union

import pandas as pd
from sqlalchemy import text
from sqlalchemy.orm import Session

from src.time_series_pred.db.db_operations import with_db_session


@with_db_session(commit=False)
def get_data(session: Session, sql: str, params: Optional[Union[list, tuple, dict]] = None) -> pd.DataFrame:
    session.rollback()
    if params:
        results = session.execute(text(sql), params)
    else:
        results = session.execute(text(sql))

    columns = results.cursor.description
    rows = results.fetchall()
    results_with_columns = [dict(zip([col[0] for col in columns], row)) for row in rows]
    df = pd.DataFrame(results_with_columns)
    return df