{"DEPLOY": {"cloud": "HIS", "infra": "AIF", "enable_doc": false, "service_name": "", "uri_prefix": "/mmae-pyxis", "config_server": {"url": "https://apig.hieds.net/api/ConfigCenter/services/saasConfigcenterGetConfig", "app_id": "904eef23d0874c1da63b14d6b1c9e946", "du": "fcst_profits_service", "environment": "similar-general", "region": "kwe4hwsimilar", "version": "1.0", "config_parts": ["9680efcebbb2437fb4fb8ae86504b454", "3e601819d60c426eba4bee5a7bbee688"]}, "api_gateway": {"authorization": {"url": "https://w3cloud.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken", "iam_url": "https://iam.his-op.huawei.com/iam/auth/token", "iam_account": "pyxis.iam"}, "authentication": {"sgov_token": "https://w3cloud.huawei.com/ApiCommonQuery/appToken/validateToken", "sgov_uri": "https://w3cloud.huawei.com/ApiCommonQuery/appToken/authorizeByURI"}}}, "LOGGER": {"level": "INFO"}, "RDB": {"default": {"datasource_name": "fin_dm_opt_fop", "tables": ["dm_fop_ict_pl_sum_t"], "echo": false}}, "OBS": {"demo_bucket": {"bucket": "fai-mlops-demo", "pool_size": 5, "max_overflow": 5, "pool_timeout": 2, "pool_recycle": 3600, "pool_pre_ping": true}}, "AIF": {"base_url": "https://console.his-op.huawei.com"}, "LIMITER": {"storage_uri": null, "strategy": "moving-window", "unit": "minute", "frequency": 10}, "APP_CONFIG": {"logging_max_body_length": 1024, "logging_separator": "\n", "logging_custom_headers": ["X-TENANTID", "X-APPID", "X-USERID"]}, "FORCAST_API": {"upload_url": "https://ai.finance.huawei.com/opt/pc/ifs/v1/version/createVersionStartForecastByFile", "forecast_result_url": "https://ai.finance.huawei.com/opt/pc/ifs/v1/version/generateTemporaryURL", "referer": "https://ai.finance.huawei.com/opt/ifs/portal", "request_max_timeout": 10, "max_duration": 900, "interval": 10, "max_consecutive_failures": 5, "token_refresh_interval": 300, "service_restart_wait_multiplier": 2, "max_service_restart_wait": 30}}