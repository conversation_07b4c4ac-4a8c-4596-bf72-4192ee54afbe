import time
import traceback
from enum import Enum
from typing import Literal, Optional, Union

from fastapi import Request, status
from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel, Field, model_validator, ValidationError
from sqlalchemy.exc import IntegrityError
from sqlalchemy.exc import TimeoutError as SqlalchemyExcTimeoutError
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import Response

from pyxis.utils.authentication.his_authentication import HisSubscriptionError, HisTokenError
from pyxis.utils.authentication.his_authentication import soa_verify_token, soa_verify_uri
from pyxis.utils.obs.his_obs_client_pool import HisObsClientPoolError
from src.utils.resource_loader import CONFIG, LOGGER
from src.utils.response_generator import response_generator

EXEMPT_ROUTES = {
    f"{CONFIG.deploy.uri_prefix}/docs",
    f"{CONFIG.deploy.uri_prefix}/redoc",
    f"{CONFIG.deploy.uri_prefix}/openapi.json",
    f"{CONFIG.deploy.uri_prefix}/test",
    "/docs",
    "/redoc",
    "/openapi.json",
    "/test"
}


class AppBizStatus(str, Enum):
    SUCCESS = "MS-00-00"
    UNKNOWN_ERROR = "MS-00-99"
    INVALID_REQUEST = "MS-00-01"
    REQUEST_FORBIDDEN = "MS-00-02"
    VALIDATION_ERROR = "MS-00-03"
    SERVICE_UNAVAILABLE = "MS-00-04"
    ITEM_ALREADY_EXISTS = "MS-00-05"


class AppBizStatusMsg(str, Enum):
    HEALTH_TEST = "Alive!"
    UNKNOWN_ERROR = "Unknown Error!"
    INVALID_REQUEST = "Invalid Request!"
    REQUEST_FORBIDDEN = "Request Forbidden!"
    SERVICE_UNAVAILABLE = "Service busy or under maintenance, please try again later!"
    ITEM_ALREADY_EXISTS = "Item already exist!"


class RequestHeaders(BaseModel):
    content_type: Optional[str] = Field(None, alias="content-type")
    x_tenantid: str = Field(..., min_length=1, max_length=100, alias="x-tenantid")
    x_appid: str = Field(..., min_length=1, max_length=100, alias="x-appid")
    x_userid: str = Field(..., min_length=1, max_length=100, alias="x-userid")
    x_traceid: str = Field(..., min_length=1, max_length=100, alias="x-traceid")
    authorization: str = Field(..., min_length=1, max_length=1000)
    method: Literal["GET", "POST", "PUT", "DELETE"] = Field(...)

    @model_validator(mode="after")
    def validate_content_type(self):
        if self.method in {"POST", "PUT"} and self.content_type != "application/json":
            raise ValueError("Content-Type must be application/json for POST and PUT requests")
        return self


class RequestMiddleware(BaseHTTPMiddleware):
    """
    请求头检查中间件
    """

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        if request.url.path not in EXEMPT_ROUTES:
            RequestHeaders(**request.headers, method=request.method)

        response = await call_next(request)
        return response


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """
    鉴权中间件
    """

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        if request.url.path not in EXEMPT_ROUTES:
            soa_verify_token(
                CONFIG.deploy.api_gateway.authentication.sgov_token,
                request.headers.get("Authorization")
            )
            soa_verify_uri(
                CONFIG.deploy.api_gateway.authentication.sgov_uri,
                request.headers.get("Authorization"),
                CONFIG.deploy.uri_prefix
            )

        response = await call_next(request)
        return response


class TimingMiddleware(BaseHTTPMiddleware):
    """
    性能计时中间件
    """

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        start_time = time.perf_counter()
        response = await call_next(request)
        process_time = time.perf_counter() - start_time
        response.headers["X-Response-Time"] = str(int(process_time * 1000))
        return response


class AuditLoggingMiddleware(BaseHTTPMiddleware):
    """
    审计日志中间件
    """

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        if request.url.path not in EXEMPT_ROUTES:
            LOGGER.info(
                f"server.call.method: {request.method} |"
                f"server.call.url: {request.url} |"
                f"server.call.tenant: {request.headers.get('X-TENANTID')} |"
                f"server.call.app: {request.headers.get('X-APPID')} |"
                f"server.call.user: {request.headers.get('X-USERID')} |"
                f"server.call.trace: {request.headers.get('X-TRACEID')} "
            )
            response = await call_next(request)
            LOGGER.info(
                f"server.call.method: {request.method} |"
                f"server.call.url: {request.url} |"
                f"server.call.status: {response.status_code} |"
                f"server.call.tenant: {request.headers.get('X-TENANTID')} |"
                f"server.call.app: {request.headers.get('X-APPID')} |"
                f"server.call.user: {request.headers.get('X-USERID')} |"
                f"server.call.trace: {request.headers.get('X-TRACEID')} |"
                f"server.call.elapsed: {response.headers.get('X-Response-Time')} ms"
            )
        else:
            response = await call_next(request)
        return response


def handle_validation_error_message(e):
    """
    覆盖FastAPI框架默认422错误格式
    """
    error_msg = []
    for err in e.errors():
        if len(err.get("loc")) == 1:
            error_msg.append(err.get("loc")[0] + ": " + err.get("msg"))
        else:
            err_key = ".".join([str(i) for i in err.get("loc")[1:]])
            error_msg.append(err_key + ": " + err.get("msg"))
    return "; ".join(error_msg)


def validation_error_handler(request: Request, exc: Union[RequestValidationError, ValidationError]):
    """
    全局请求参数格式错误捕获，用于覆盖FastAPI默认的处理函数
    """
    return response_generator(
        status.HTTP_422_UNPROCESSABLE_ENTITY, AppBizStatus.VALIDATION_ERROR,
        handle_validation_error_message(exc), list()
    )



ERROR_MAPPING = {
    HisTokenError: (
        status.HTTP_401_UNAUTHORIZED, AppBizStatus.INVALID_REQUEST, AppBizStatusMsg.INVALID_REQUEST, list()
    ),
    HisSubscriptionError: (
        status.HTTP_403_FORBIDDEN, AppBizStatus.REQUEST_FORBIDDEN, AppBizStatusMsg.REQUEST_FORBIDDEN, list()
    ),
    (RequestValidationError, ValidationError): (
        status.HTTP_422_UNPROCESSABLE_ENTITY, AppBizStatus.VALIDATION_ERROR,
        lambda err: handle_validation_error_message(err), list()
    ),
    (SqlalchemyExcTimeoutError, HisObsClientPoolError): (
        status.HTTP_503_SERVICE_UNAVAILABLE, AppBizStatus.SERVICE_UNAVAILABLE,
        AppBizStatusMsg.SERVICE_UNAVAILABLE, list()
    ),
    IntegrityError: (
        status.HTTP_409_CONFLICT, AppBizStatus.ITEM_ALREADY_EXISTS, AppBizStatusMsg.ITEM_ALREADY_EXISTS, list()
    )
}

DEFAULT_ERROR_RESPONSE = (
    status.HTTP_500_INTERNAL_SERVER_ERROR, AppBizStatus.UNKNOWN_ERROR, AppBizStatusMsg.UNKNOWN_ERROR, list()
)


def handle_exception(e):
    for error_types, response_data in ERROR_MAPPING.items():
        if isinstance(e, error_types):
            http_code, status_code, message, data = response_data
            if callable(message):
                message = message(e)
            return response_generator(http_code, status_code, message, data)

    return response_generator(*DEFAULT_ERROR_RESPONSE)


class ExceptionMiddleware(BaseHTTPMiddleware):
    """
    异常捕获中间件
    """

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        try:
            response = await call_next(request)
        except Exception as e:
            LOGGER.debug(
                f"server.call.method: {request.method} |"
                f"server.call.url: {request.url} |"
                f"server.call.tenant: {request.headers.get('X-TENANTID')} |"
                f"server.call.app: {request.headers.get('X-APPID')} |"
                f"server.call.user: {request.headers.get('X-USERID')} |"
                f"server.call.trace: {request.headers.get('X-TRACEID')} |"
                f"server.call.debug: {traceback.format_exc()}"
            )

            response = handle_exception(e)
        return response
