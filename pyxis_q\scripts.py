CREATE_TASK = """  
INSERT INTO   
    {schema}.{table}   
(  
    task_name, task_id, task_group,  
    args, task_status, priority,  
    execution_count, max_retries, time_limit,  
    creation_date, expiration_date,  
    ignore_before, last_update_date, retry_interval_in_seconds,  
    created_by, last_update_by,  
    task_result, error, elapsed_sec  
)  
VALUES (  
    %(task_name)s, %(task_id)s, %(task_group)s,  
    %(args)s, %(task_status)s, %(priority)s,  
    0, %(max_retries)s, %(time_limit)s,  
    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  
    CURRENT_TIMESTAMP AT TIME ZONE 'UTC' + (%(ttl_seconds)s || ' seconds')::interval,  
    CURRENT_TIMESTAMP AT TIME ZONE 'UTC' + (%(delay)s || ' seconds')::interval,  
    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  
    %(retry_interval_in_seconds)s,  
    %(created_by)s, %(created_by)s,  
    %(task_result)s, %(error)s, %(elapsed_sec)s  
)  
"""

CANCEL_TASK_BY_ID = """
DELETE FROM 
    {schema}.{table}
WHERE
    task_id = %(task_id)s
    AND task_status = 'PENDING'
"""

FETCH_TASK = """
UPDATE 
    {schema}.{table}  
SET 
    task_status = 'PROCESSING',
    last_update_date =  CURRENT_TIMESTAMP AT TIME ZONE 'UTC' 
WHERE 
    task_group = %(task_group)s
    AND task_id IN ( 
        SELECT 
            task_id 
        FROM 
            {schema}.{table}  
        WHERE  
            task_group = %(task_group)s  
            AND task_status = 'PENDING'  
            AND execution_count < max_retries + 1  
            AND ignore_before < CURRENT_TIMESTAMP AT TIME ZONE 'UTC'   
            AND expiration_date > CURRENT_TIMESTAMP AT TIME ZONE 'UTC'   
        ORDER BY 
            {strategy}  
        LIMIT {pre_fetch}  
    ) 
AND task_status = 'PENDING'  
RETURNING {cols}
"""

UPDATE_TASK = """
UPDATE 
    {schema}.{table}
SET
    execution_count = %(execution_count)s,
    task_status = %(task_status)s,
    task_result = %(task_result)s,
    error = %(error)s,
    elapsed_sec = %(elapsed_sec)s,
    last_update_date = %(last_update_date)s,
    ignore_before = %(ignore_before)s
WHERE
    task_id = %(task_id)s
"""
