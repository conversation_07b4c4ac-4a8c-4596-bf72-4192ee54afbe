from datetime import datetime

# dimension_subcategory_pred_df 量纲子类场景
# 先判断结果表df是否有均本>均价的现象
# 同一个period_id的同一个dim里面，如果有这种情况，就把这个period_id 的这个dim的数据用另外的数替换
import pandas as pd

from src.utils.resource_loader import LOGGER as log
import numpy as np

# 均本、均价，只涉及到量纲分组和量纲子类的场景
#    * 判断逻辑：同一预测对象，预测有均本>均价的现象(只要有一个点有这种情况就算，比如只有202412一个时间点，均本大于均价，也认为预测的不合理)
#    * 替换逻辑：整条时序预测结果用去年同期结果替换，如预测202407-202412，用202306-202312值作为预测结果


def transform_date(row):
    row_pre = row[:-3]
    row_suffix = row[-3:]
    if not row_suffix.isdigit():
        row = row_pre
    if '-' in row:
        row = row.replace("-", "")
    row = datetime.strptime(row, '%Y%m')
    row = pd.to_datetime(row) + pd.DateOffset(years=1)
    row = row.strftime("%Y%m")
    return row


def group_transform_date(row):
    if type(row) == str:
        #     如果后三字符不是数字，则去掉转为"%Y%m"，如果有横杠则去掉
        if '-' in row:
            row = row.replace("-", "")
    else:
        try:
            row = row.strftime("%Y%m")
        except Exception:
            row = row.strftime("%Y-%m")
            row = row.replace("-", "")

    if len(row) > 6:
        row = row[:6]
    return row


# 当预测出的单位成本（unit_cost_fcst）高于单位售价（unit_price_fcst）时，用历史同期数据替换
def correct_cost_price_anomalies(pred_df, his_df, groupby_columns):
    grouped = pred_df.groupby(groupby_columns)
    for name, group in grouped:
        try:
            group_inx = group.index
            group_on = groupby_columns + ['target_period']
            if (group['unit_cost_fcst'] > group['unit_price_fcst']).sum() > 0:
                # 获取列和列值,
                condition = dict(zip(groupby_columns, name))  # {'dim':A,'period_id':202407}
                query_parts = [f"{k} == {repr(v)}" for k, v in condition.items()]
                query_str = " and ".join(query_parts)

                # 从历史数据中筛选对应维度的记录
                filtered_data = his_df.query(query_str)
                # target_period用上一年的
                filtered_data.loc[:, 'target_period'] = filtered_data['target_period'].apply(transform_date)
                group.loc[:, 'target_period'] = group['target_period'].apply(group_transform_date)

                # 去重并准备用于替换的历史数据
                filtered_data.drop_duplicates(subset=group_on, keep='first', inplace=True)

                # filtered_data按target_period一个一个替换原有的pred_df的unit_cost, unit_price
                merge_df = \
                    pd.merge(group, filtered_data[groupby_columns + ['unit_cost', 'unit_price', 'target_period']],
                             on=group_on, how='inner')[group_on + ['unit_cost', 'unit_price']]
                group = pd.merge(group, merge_df, on=group_on, how='left')
                group.drop_duplicates(subset=group_on, keep='first', inplace=True)
                pred_df.loc[group_inx, ['unit_cost_fcst', 'unit_price_fcst']] = group[
                    ['unit_cost_fcst', 'unit_price_fcst']].values
        except Exception as e:
            log.error("预测后处理参数-{}错误:{}".format(name, e))


def after_process_ratio(pred_df, his_df, groupby_columns, his_param, pre_param):
    grouped = pred_df.groupby(groupby_columns)
    for name, group in grouped:
        try:
            group_inx = group.index
            # 获取去年全年的ratio
            # 获取列和列值
            condition = dict(zip(groupby_columns, name))  # {'dim':A,'period_id':202407}
            query_parts = [f"{k} == {repr(v)}" for k, v in condition.items()]
            query_str = " and ".join(query_parts)
            filtered_data = his_df.query(query_str)
            year_ratio_values = filtered_data[his_param]
            if year_ratio_values.empty:
                year_ratio = 0
            else:
                year_ratio = filtered_data[filtered_data[his_param].notna()][his_param]
                if not year_ratio.empty:
                    year_ratio = max(year_ratio)
                else:
                    year_ratio = 0
            for i, g in group.iterrows():
                if abs(g[pre_param]) > 1:
                    if abs(year_ratio) > 1:
                        # 获取列和列值
                        year_ratio = 0
                        pred_df.loc[group_inx, pre_param] = 0
                    else:
                        pred_df.loc[group_inx, pre_param] = year_ratio
        except Exception as e:
            log.error('参数：{}-分组赋值报错：{}'.format(name, e))


def post_process_non_nagtive(df: pd.DataFrame,
                             his_df: pd.DataFrame,
                             dim: list,
                             data_cols: list):
    """
    替换 df 中负值的 unit_cost 和 cost_price 为 his_df 中对应的ytd_data值,
    如果还是负数，就替换成0

    参数:
        df (pd.DataFrame): 当前预测数据，包含列 [dim+ ['unit_cost_fcst', 'cost_price_fcst', 'carryover_rate']]
        his_df (pd.DataFrame): 历史数据，包含列 [dim + ['unit_cost_ytd_data', 'unit_price_ytd_data']]
        dim (List[str]): 用于匹配的维度列名列表
        data_cols (List[str]): 需要处理的列名列表（如 ['unit_cost', 'unit_price']）

    返回:
        pd.DataFrame: 替换负值后的 df
    """
    try:

        # 基于 dim 合并两个 DataFrame
        ytd_data_cols = [col+'_ytd_data' for col in data_cols]
        ytd_df = his_df[dim + ytd_data_cols].copy()
        ytd_df = ytd_df.drop_duplicates()
        merged = pd.merge(df, ytd_df, on=dim, how='left')

        # 替换负值
        for col in data_cols:
            if col+'_fcst' in merged.columns and col + '_ytd_data' in merged.columns:
                if col+'_fcst_upper' in merged.columns and col+'_fcst_lower' in merged.columns:
                    merged[col + '_fcst_upper'] = np.where(
                        merged[col + '_fcst'] < 0,
                        np.where(
                            merged[col + '_ytd_data'] < 0,
                            0,
                            merged[col + '_ytd_data']
                        ),
                        merged[col + '_fcst_upper']
                    )
                    merged[col + '_fcst_lower'] = np.where(
                        merged[col + '_fcst'] < 0,
                        np.where(
                            merged[col + '_ytd_data'] < 0,
                            0,
                            merged[col + '_ytd_data']
                        ),
                        merged[col + '_fcst_lower']
                    )
                merged[col + '_fcst'] = np.where(
                    merged[col + '_fcst'] < 0,
                    np.where(
                        merged[col + '_ytd_data'] < 0,
                        0,
                        merged[col + '_ytd_data']
                    ),
                    merged[col + '_fcst']
                )

        # 删除冗余列
        merged.drop(columns=ytd_data_cols, inplace=True, errors='ignore')

        # 将结果重新赋值给原始 df
        df[:] = merged.values
    except Exception as e:
        print(f"Error occurred during non-nagtive processing: {e}")
    return df
