import sys

from fastapi.testclient import TestClient

from tests.conftest import mock_module

sys.modules["src.utils.resource_loader"] = mock_module

from src.router.app import router
from src.utils.resource_loader import CONFIG


client = TestClient(router.app)


def test_start_time_series_pred():
    response = client.post(
        f"{CONFIG.deploy.service_name}/v1/prediction-tasks/start",
        json={
            "task_name": "时序预测",
            "params": {
                "pred_version": 202509
            },
            "env": "local"
        }
    )

    assert response.status_code == 200
    result = response.json()
    assert result['statusCode'] == 'OK'


def test_start_expert_fusion_optimization():
    response = client.post(
        f"{CONFIG.deploy.service_name}/v1/prediction-tasks/start",
        json={
            "task_name": "融合调优",
            "params": {
                "pred_version": 202509
            },
            "env": "local"
        }
    )

    assert response.status_code == 200
    result = response.json()
    assert result['statusCode'] == 'OK'