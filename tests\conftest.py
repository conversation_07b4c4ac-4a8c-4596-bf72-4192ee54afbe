import json
import os
from unittest.mock import MagicMock, patch

import pytest

from pyxis.utils.config_template import Config

tests_dir = os.path.dirname(__file__)
project_dir = os.path.dirname(tests_dir)
os.chdir(project_dir)

with open(os.path.join("src", "config", f"LOCAL.json"), "r", encoding="utf-8") as f:
    mock_config = json.load(f)

mock_config_loader = MagicMock()
mock_config_loader.config = mock_config
mock_config_loader.get_rdb_pools.return_value = {'default', MagicMock()}
mock_config_loader.get_logger.return_value = MagicMock()

rdb_pool_mock = MagicMock()
rdb_pool_mock.get_session = lambda : MagicMock()

mock_module = type(
    'MockResourceLoader', (), {
        'CONFIG_LOADER': mock_config_loader,
        'CONFIG': Config(**mock_config),
        "RDB_POOL": rdb_pool_mock,
        "LOGGER": MagicMock(),
        "LIMITER": MagicMock(),
        "PRODUCER": MagicMock(),
        "load_config": lambda: mock_config
    }
)


@pytest.fixture(autouse=True)
def mock_soa_verify_token():
    with patch("pyxis.utils.authentication.his_authentication.soa_verify_token") as mock:
        mock.return_value = MagicMock()
        yield mock


@pytest.fixture(autouse=True)
def mock_env_vars():
    with patch.dict(os.environ,
{
        "PROBLEM_ID": "111",
        "TASK_ID": "222",
        "UNIT_COST_SOLUTIONID": "333",
        "UNIT_PRICE_SOLUTIONID": "444",
        "OTHER_PARAM_SOLUTIONID": "555"
        }):
        yield