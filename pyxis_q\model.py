from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from typing import Union, Dict, List

JSONValue = Union[str, int, float, bool, None, List['JSONValue'], Dict[str, 'JSONValue']]


@dataclass
class TaskDbModel:
    task_id: str
    task_name: str
    task_group: str
    task_status: str
    priority: int
    execution_count: int
    max_retries: int
    time_limit: int
    retry_interval_in_seconds: int
    ignore_before: datetime
    creation_date: datetime
    expiration_date: datetime
    last_update_date: datetime
    created_by: str
    last_update_by: str
    args: dict = field(default_factory=dict)
    task_result: JSONValue = None
    error: JSONValue = None
    elapsed_sec: Decimal = None

    def to_dict_exclude(self, exclude: set = None) -> dict:
        exclude = exclude or set()
        return {k: v for k, v in self.__dict__.items() if k not in exclude}

    def to_dict_include(self, include: set = None) -> dict:
        include = include or set()
        return {k: v for k, v in self.__dict__.items() if k in include}
