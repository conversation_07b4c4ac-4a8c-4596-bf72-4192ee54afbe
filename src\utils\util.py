import os
import zoneinfo
from datetime import datetime
from pathlib import Path
from typing import List

import pandas as pd

from src.utils.resource_loader import LOGGER as logger


def current_date():
    now = datetime.now(zoneinfo.ZoneInfo("Asia/Shanghai"))
    year = now.year
    month = f"{now.month:02d}"
    return int(f"{year}{month}")


def check_null_values(df: pd.DataFrame, cols_to_check: list):
    null_stats = pd.DataFrame({
        '空值数量': df[cols_to_check].isna().sum(),
        '空值占比': df[cols_to_check].isna().mean().round(4) * 100  # 转换为百分比
    })
    if not null_stats.empty:
        logger.info(
            f"来源表,有空值，{null_stats.to_string()}"
        )


def save_to_local(df: pd.DataFrame, filename: str):
    if os.environ.get('SAVE_TO_LOCAL', 'false') == 'false':
        return
    timestamp = datetime.now(zoneinfo.ZoneInfo("Asia/Shanghai")).strftime('%Y%m%d_%H%M%S')
    df.to_csv(Path(__file__).parent / "results" / f"{filename}_{timestamp}.csv")


def aggregate_pred_data(
        df: pd.DataFrame,
        group_by_cols: List[str],
        pred_cols: List[str],
        interval_cols: List[str]
) -> pd.DataFrame:
    """
    聚合预测数据
    :param df: 输入DataFrame
    :param group_by_cols: 聚合维度
    :param pred_cols: 预测列列表
    :param interval_cols: 区间预测列列表
    :return: 聚合后的DataFrame
    """
    agg_dict = {}
    agg_dict.update({f"{col}_fcst": 'max' for col in pred_cols})
    agg_dict.update({f"{col}_fcst_upper": 'max' for col in interval_cols})
    agg_dict.update({f"{col}_fcst_lower": 'max' for col in interval_cols})
    agg_dict.update({f"{col}_fcst_conf": 'max' for col in interval_cols})

    aggregated_df = df.groupby(group_by_cols).agg(agg_dict).reset_index()

    # 如果存在空值，进行二次聚合
    if aggregated_df.isnull().values.any():
        aggregated_df = aggregated_df.groupby(group_by_cols).agg(agg_dict).reset_index()

    return aggregated_df
