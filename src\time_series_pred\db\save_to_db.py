import os
from datetime import datetime
from typing import Union

import numpy as np
import pandas as pd
from sqlalchemy import MetaData, Table, text
from sqlalchemy.orm import Session

from src.time_series_pred.common.dimension_constant import target_tables
from src.time_series_pred.db.db_operations import with_db_session
from src.utils.resource_loader import LOGGER as log, RDB_POOL, CONFIG_LOADER

cols_should_be_saved_dim = ['period_id', 'time_window_code', 'fcst_type', 'scenarios',
                            'target_period',
                            'bg_code', 'bg_name',
                            'oversea_code', 'oversea_desc',
                            'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name',
                            'currency',
                            'dimension_group_code', 'dimension_group_cn_name', 'dimension_group_en_name',
                            'dimension_subcategory_code', 'dimension_subcategory_cn_name',
                            'dimension_subcategory_en_name',
                            'rev_percent_fcst',
                            'unit_price_fcst_conf', 'unit_price_fcst', 'unit_price_fcst_upper', 'unit_price_fcst_lower',
                            'unit_cost_fcst_conf', 'unit_cost_fcst', 'unit_cost_fcst_upper', 'unit_cost_fcst_lower',
                            'mgp_rate_before_fcst_conf', 'mgp_rate_before_fcst', 'mgp_rate_before_fcst_upper',
                            'mgp_rate_before_fcst_lower',
                            'unit_price_fcst_acc', 'unit_cost_fcst_acc',
                            'carryover_ratio_fcst']

cols_should_be_saved_no_dim = ['period_id', 'time_window_code', 'fcst_type', 'scenarios',
                               'target_period',
                               'bg_code', 'bg_name',
                               'oversea_code', 'oversea_desc',
                               'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name',
                               'currency',
                               'equip_rev_after_fcst_conf', 'equip_rev_after_fcst', 'equip_rev_after_fcst_upper',
                               'equip_rev_after_fcst_lower',
                               'mgp_rate_after_fcst_conf', 'mgp_rate_after_fcst', 'mgp_rate_after_fcst_upper',
                               'mgp_rate_after_fcst_lower',
                               'mca_adjust_ratio_fcst',
                               'mgp_adjust_ratio_fcst']

replace_col = ['carryover_ratio_fcst', 'rev_percent_fcst', 'unit_price_fcst',
               'mgp_rate_before_fcst_upper', 'mgp_rate_after_fcst_lower', 'equip_rev_after_fcst_upper',
               'mgp_rate_after_fcst_conf', 'unit_cost_fcst_upper', 'unit_cost_fcst_acc', 'unit_price_fcst_acc',
               'mgp_rate_after_fcst', 'unit_price_fcst_upper', 'unit_price_fcst_conf', 'unit_cost_fcst_lower',
               'mgp_rate_before_fcst_lower', 'equip_rev_after_fcst', 'mca_adjust_ratio_fcst', 'unit_price_fcst_lower',
               'equip_rev_after_fcst_lower', 'mgp_rate_before_fcst', 'mgp_adjust_ratio_fcst',
               'mgp_rate_before_fcst_conf',
               'unit_cost_fcst', 'mgp_rate_after_fcst_upper', 'unit_cost_fcst_conf', 'equip_rev_after_fcst_conf']


def is_save_enabled() -> bool:
    """检查是否启用数据库保存"""
    return os.environ.get('SAVE_DB_ENABLED', 'true').lower() == 'true'


def save_to_db(df: pd.DataFrame, scenarios: str = Union['DIM', 'LV2'], is_encrypt: bool = False):
    """
    保存预测结果到数据库
    """

    if scenarios == 'DIM':
        df.rename(columns=lambda col: col.replace('mgp_ratio_', 'mgp_rate_before_')
        if col.startswith('mgp_ratio_') else col, inplace=True)
        df.rename(columns=lambda col: col.replace('carryover_rate_', 'carryover_ratio_')
        if col.startswith('carryover_rate_') else col, inplace=True)
        cols_should_be_saved = cols_should_be_saved_dim
        target_table = 'dm_fop_dimension_fcst_t'
    else:
        df.rename(columns=lambda col: col.replace('mgp_ratio_after_', 'mgp_rate_after_')
        if col.startswith('mgp_ratio_after_') else col, inplace=True)
        df.rename(columns=lambda col: col.replace('equip_rev_cons_after_amt_fcst', 'equip_rev_after_fcst')
        if col.startswith('equip_rev_cons_after_amt_fcst') else col, inplace=True)
        cols_should_be_saved = cols_should_be_saved_no_dim
        target_table = 'dm_fop_dimension_lv2_fcst_t'
    intersection = list(set(cols_should_be_saved) & set(df.columns))
    df = df[intersection]
    df['fcst_type'] = 'YTD法'
    df['target_period'] = pd.to_datetime(df['target_period']).dt.strftime("%Y%m")
    # 加币种
    df['currency'] = 'CNY'
    df['remark'] = ''  # 备注，默认为空字符串
    # 添加 created_by，默认为 -1
    df['created_by'] = -1
    # 添加 creation_date，默认为当前时间
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    df['creation_date'] = current_time
    # # 添加 last_updated_by，默认为 -1
    df['last_updated_by'] = -1
    # 添加 last_update_date，默认为当前时间
    df['last_update_date'] = current_time
    # # 添加 del_flag，默认为 'N'
    df['del_flag'] = 'N'
    # 确保其他列的数据类型正确
    # 显式转换数值列，替换 NaN 为 None
    for col in replace_col:
        if col in df.columns:
            # 替换非数值为NaN--再替换为None
            df[col] = pd.to_numeric(df[col], errors='coerce')
            df[col] = df[col].astype(object).replace({np.nan: None})

    if is_save_enabled():
        if is_encrypt:
            secret_key = CONFIG_LOADER.get_accounts_by_name('profits.gs.dataKey')['password']
            insert_to_database_with_encrypt(df, target_table, secret_key)
        else:
            _insert_to_database(df, target_table)


@with_db_session(commit=True)
def soft_del_pred_version_data(session: Session, target_table: str, pred_version: int):
    metadata = MetaData()
    table = Table(target_table, metadata, autoload_with=RDB_POOL._engine)
    log.info(f"准备软删除 {target_table} 表中-会计期={pred_version} 的数据!")
    session.execute(
        table.update()
        .where(table.c.period_id == pred_version)
        .values({table.c.del_flag: "Y"}))

    log.info(f"成功软删除 {target_table} 表中-会计期={pred_version} 的数据")


@with_db_session(commit=True)
def _insert_to_database(session: Session, df: pd.DataFrame, table_name: str) -> None:
    try:
        data_to_insert = df.to_dict(orient='records')
        log.info(f"准备插入 {len(data_to_insert)} 条记录到表 {table_name}")

        engine = RDB_POOL._engine
        metadata = MetaData()
        table = Table(table_name, metadata, autoload_with=engine)

        session.execute(table.insert(), data_to_insert)
        log.info(f"成功保存 {len(data_to_insert)} 条记录到数据库表 {table_name}")

    except Exception as e:
        log.error(f"数据库插入失败 - 表: {table_name}, 错误: {str(e)}")
        raise



@with_db_session(commit=True)
def insert_to_database_with_encrypt(session: Session, df: pd.DataFrame, table_name: str, secret_key: str) -> None:
    if table_name not in target_tables:
        raise ValueError("Invalid table")

    df = df.rename(columns={'unit_cost_fcst': 'unit_cost_fcst_encrypt'})
    data_to_insert = df.to_dict(orient='records')
    log.info(f"准备插入 {len(data_to_insert)} 条记录到表 {table_name}")
    if not data_to_insert:
        log.info("没有数据需要插入")
        return

    # 构造 VALUES 部分
    values_clause = []
    for i, row in df.iterrows():
        formatted_values = []
        for col_name, val in row.items():
            if col_name == 'unit_cost_fcst_encrypt':
                # 如果原值是 NaN，则仍为 NULL
                if pd.isna(val):
                    formatted_values.append('NULL')
                else:
                    try:
                        formatted_values.append(f"gs_encrypt({str(val)}, '{secret_key}', 'aes128', 'cbc', 'sha256')")
                    except (ValueError, TypeError):
                        formatted_values.append('NULL')
            else:
                # 其他列正常处理
                if pd.isna(val):
                    formatted_values.append('NULL')
                elif isinstance(val, str):
                    escaped_val = val.replace("'", "''")
                    formatted_values.append(f"'{escaped_val}'")
                elif isinstance(val, (int, float)):
                    formatted_values.append(str(val))
                else:
                    formatted_values.append(f"'{str(val)}'")
        if i == 0:
            values_clause.append(f"({', '.join(formatted_values)})")
        else:
            values_clause.append(f",({', '.join(formatted_values)})")
    columns_str = ', '.join([f'{col}' for col in df.columns])
    sql = f"""INSERT INTO "{table_name}" ({columns_str}) VALUES {''.join(values_clause)}"""
    session.execute(text(sql))
    log.info(f"成功保存 {len(data_to_insert)} 条记录到数据库表 {table_name}")

