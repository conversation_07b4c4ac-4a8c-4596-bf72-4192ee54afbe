import json
import re
from dataclasses import asdict, dataclass, field, fields
from datetime import datetime
from typing import List, Set
from uuid import uuid4

import psycopg2
import psycopg2.extras

from pyxis_q.constant import TaskStatus
from pyxis_q.model import TaskDbModel
from pyxis_q.scripts import CANCEL_TASK_BY_ID, CREATE_TASK

_ALL_TASk_COLS = set(key.name for key in fields(TaskDbModel))


class PyxisQConnectorError(Exception):
    def __init__(self, message: str):
        super().__init__(message)
        self.message = message


@dataclass(frozen=True)
class Task:
    """
    任务对象

    Parameters
    ----------
    task_name : str
        任务名称
        用于标识执行什么任务。消费者根据 task_name 确定执行内容，类似 Web API 的 URI 唯一资源标识符
    created_by : str
        任务提交者
        身份字段，同时也是审计字段
    args : dict
        任务入参
        如注册的任务函数需要 key1, key2 两个入参，则 args={"key1": value1, "key2": value2}
        value 需要是可 json 序列化的对象
    task_id : str
        任务id
        如果用户输入则认为同一个任务防止重复提交，如果用户不输入视为不同任务自动生成uuid4
    task_group : str
        路由键
        用于逻辑多租隔离，任务组类似于消息队列中的 topic
        默认为 "default"
    ttl_seconds : int
        过期时间(秒)
        提交任务后，如果超过 ttl_seconds 时间，则不会再被消费者拉取消费
        默认为 60
    delay : int
        推迟时间(秒)
        提交任务后，超过 delay 时间，才会被消费者拉取。防止调用方还没有收到提交确认时(网络原因等等)，消费者已经消费
        默认为 1
    max_retries : int
        重试次数
        任务报错(程序报错、超时等)后的重试次数 超过过期时间也不再重试
        默认为 0, 不重试
    retry_interval_in_seconds : int
        重试间隔(秒)
        默认为 3
    priority : int
        优先级
        当消费者消费策略为 "priority_fifo" 时，优先级数字越大优先级越高 上一优先级的任务处理完后才会处理下一优先级任务
        默认为 0
    time_limit : int
        任务在消费者中执行的最大时间(秒)，超时则失败
        默认为 60
    """
    task_name: str
    created_by: str
    args: dict
    task_id: str = field(default_factory=lambda: str(uuid4()))
    task_group: str = "default"
    ttl_seconds: int = 60
    delay: int = 1
    max_retries: int = 0
    retry_interval_in_seconds: int = 3
    priority: int = 0
    time_limit: int = 60


@dataclass(frozen=True)
class QueryFilter:
    """
    Parameters
    ----------
    task_id : Set[str]
        任务id集合
    task_name : Set[str]
        任务名称集合
    task_group : Set[str]
        路由键集合
    creation_date_start : datetime
        创建时间最小值, 含等于。要求为标准 UTC 时间
    creation_date_end : datetime
        创建时间最大值, 含等于。要求为标准 UTC 时间
    last_update_date_start : datetime
        最后更新时间最小值, 含等于。要求为标准 UTC 时间
    last_update_date_end : datetime
        最后更新时间最大值, 含等于。要求为标准 UTC 时间
    is_expired : bool
        是否过期, True: 过期 , False: 未过期, None: 不限制
        默认为 None
    task_status : Set[str]
        任务状态集合
        可选值包括 PENDING, PROCESSING, SUCCESS, FAILED
    limit : int
        分页大小
        默认为 10
    offset : int
        第几页
        默认为 0
    show_args : bool
        查询结果是否包含任务入参(args)
        默认为 True
    show_result : bool
        查询结果是否包含任务结果(task_result)
        默认为 True
    show_error : bool
        查询结果是否包含任务报错(error)
        默认为 True
    """
    task_id: Set[str] = None
    task_name: Set[str] = None
    task_group: Set[str] = frozenset({"default"})
    creation_date_start: datetime = None
    creation_date_end: datetime = None
    last_update_date_start: datetime = None
    last_update_date_end: datetime = None
    is_expired: bool = None
    task_status: Set[str] = None
    limit: int = 10
    offset: int = 0
    show_args: bool = True
    show_result: bool = True
    show_error: bool = True


def _query_builder(filters: QueryFilter) -> str:
    query = """
    SELECT
        {cols}
    FROM
        {schema}.{table}
    WHERE
        task_group IN %(task_group)s 
    """

    if filters.task_id:
        query += "AND task_id IN %(task_id)s "
    if filters.task_name:
        query += "AND task_name IN %(task_name)s "
    if filters.creation_date_start:
        query += "AND creation_date >= %(creation_date_start)s "
    if filters.creation_date_end:
        query += "AND creation_date <= %(creation_date_end)s "
    if filters.last_update_date_start:
        query += "AND last_update_date >= %(last_update_date_start)s "
    if filters.last_update_date_end:
        query += "AND last_update_date <= %(last_update_date_end)s "

    if filters.is_expired is not None:
        if filters.is_expired:
            query += "AND expiration_date <= CURRENT_TIMESTAMP AT TIME ZONE 'UTC' "
        else:
            query += "AND expiration_date > CURRENT_TIMESTAMP AT TIME ZONE 'UTC' "

    if filters.task_status:
        query += "AND task_status IN %(task_status)s "

    query += "LIMIT %(limit)s OFFSET %(offset)s "
    return query


def _select_cols(filters: QueryFilter) -> tuple:
    del_cols = set()
    if not filters.show_args:
        del_cols.add("args")
    if not filters.show_result:
        del_cols.add("task_result")
    if not filters.show_error:
        del_cols.add("error")
    return tuple(_ALL_TASk_COLS - del_cols)


def _transform_filters_for_query(filters: QueryFilter) -> dict:
    query_parm = asdict(filters)
    query_parm["task_id"] = tuple(query_parm["task_id"]) if query_parm["task_id"] else None
    query_parm["task_name"] = tuple(query_parm["task_name"]) if query_parm["task_name"] else None
    query_parm["task_group"] = tuple(query_parm["task_group"]) if query_parm["task_group"] else None
    query_parm["task_status"] = tuple(query_parm["task_status"]) \
        if query_parm["task_status"] else None
    return query_parm


def _init_task(task: Task) -> dict:
    row = asdict(task)
    row["args"] = json.dumps(obj=row["args"], ensure_ascii=False)
    row["task_status"] = TaskStatus.PENDING.value
    row["last_update_by"] = row["created_by"]
    row["elapsed_sec"] = -1.0
    row["task_result"] = None
    row["error"] = json.dumps(obj={}, ensure_ascii=False)
    return row


def _psycopg2_sql_to_sqlalchemy(query: str) -> str:
    """
    Replace %(xxx)s with :xxx
    """
    return re.sub(pattern=r"%\((\w+)\)s", repl=r":\1", string=query)


class Psycopg2Connector:
    def __init__(self, schema: str = "public", table: str = "task_q"):
        super().__init__()
        self.schema = schema
        self.table = table

    def send_tasks(self, tasks: List[Task], session: psycopg2.extensions.connection, commit: bool = False) -> List[str]:
        if len(set(task.task_id for task in tasks)) != len(tasks):
            raise PyxisQConnectorError("task_id must be unique")
        with session.cursor() as cur:
            psycopg2.extras.execute_batch(
                cur=cur,
                sql=CREATE_TASK.format(schema=self.schema, table=self.table),
                argslist=[_init_task(task) for task in tasks]
            )
        if commit:
            session.commit()
        return [task.task_id for task in tasks]

    def get_tasks(self, filters: QueryFilter, session: psycopg2.extensions.connection) -> List[TaskDbModel]:
        cols = _select_cols(filters)
        query_sql = _query_builder(filters)
        with session.cursor() as cur:
            cur.execute(
                query=query_sql.format(schema=self.schema, table=self.table, cols=", ".join(cols)),
                vars=_transform_filters_for_query(filters)
            )
            tasks = cur.fetchall()
        return [TaskDbModel(**dict(zip(cols, task))) for task in tasks]

    def cancel_task(
        self, task_id: str, session: psycopg2.extensions.connection, commit: bool = False
    ) -> bool:
        """
        Delete a task from the broker if status is PENDING
        Set the commit flag to True to ensure that the task is canceled immediately
        """
        with session.cursor() as cur:
            cur.execute(
                query=CANCEL_TASK_BY_ID.format(schema=self.schema, table=self.table),
                vars={"task_id": task_id}
            )
        if commit:
            session.commit()
        return cur.rowcount > 0


class SQLAlchemyConnector:
    def __init__(self, schema: str = "public", table: str = "task_q"):
        super().__init__()
        from sqlalchemy import text
        self.schema = schema
        self.table = table
        self.text = text

    def send_tasks(self, tasks: List[Task], session, commit: bool = False) -> List[str]:
        if len(set(task.task_id for task in tasks)) != len(tasks):
            raise PyxisQConnectorError("task_id must be unique")
        session.execute(
            statement=self.text(
                (_psycopg2_sql_to_sqlalchemy(CREATE_TASK)).format(schema=self.schema, table=self.table)
            ),
            params=[_init_task(task) for task in tasks]
        )
        if commit:
            session.commit()
        return [task.task_id for task in tasks]

    def get_tasks(self, filters: QueryFilter, session) -> List[TaskDbModel]:
        cols = _select_cols(filters)
        query_sql = _psycopg2_sql_to_sqlalchemy(_query_builder(filters))
        tasks = session.execute(
            statement=self.text(query_sql.format(schema=self.schema, table=self.table, cols=", ".join(cols))),
            params=_transform_filters_for_query(filters)
        ).all()
        return [TaskDbModel(**dict(zip(cols, task))) for task in tasks]

    def cancel_task(self, task_id: str, session, commit: bool = False) -> bool:
        sql = _psycopg2_sql_to_sqlalchemy(CANCEL_TASK_BY_ID)
        result = session.execute(
            statement=self.text(sql.format(schema=self.schema, table=self.table)),
            params={"task_id": task_id}
        )
        if commit:
            session.commit()
        return result.rowcount > 0
