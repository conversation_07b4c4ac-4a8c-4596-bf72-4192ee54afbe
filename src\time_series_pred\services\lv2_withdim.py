from pathlib import Path

from src.time_series_pred.common.dimension_constant import lv2_dim, dimension_lv2_fcst_group_by_cols
from src.time_series_pred.db.get_data import get_data
from src.time_series_pred.db.save_to_db import save_to_db
from src.time_series_pred.services.profits_sequence_pred import integrate_results
from src.utils.util import check_null_values, aggregate_pred_data, save_to_local

# 预测因子
lv2_withdim_pred_lst = ['mgp_adjust_ratio', 'mca_adjust_ratio']
lv2_nondim_pred_lst = ['mgp_ratio_after', 'mgp_adjust_ratio', 'mca_adjust_ratio', 'equip_rev_cons_after_amt']
# 区间预测
cols_to_check = lv2_withdim_pred_lst+lv2_nondim_pred_lst


def profit_forecast(pred_version: int):
    try:
        sql_path = Path(__file__).parent.parent / "sql" / "lv2_withdim.sql"
        with open(sql_path, "r", encoding="utf-8") as f:
            sql_template = f.read()
        sql_query = sql_template.format(pred_version=pred_version)
        df = get_data(sql_query)
    except Exception as e:
        raise RuntimeError(f"Database query failed: {str(e)}") from e

    # 检查结果
    check_null_values(df, cols_to_check)
    # 有量纲LV2
    lv2_withdim_his_df = df[df['scenarios'].isin(['量纲子类', '量纲分组'])]

    lv2_withdim_pred_df = integrate_results(his_df=lv2_withdim_his_df, dim=lv2_dim, pred_cols=lv2_withdim_pred_lst,
                                            period_col='target_period', pred_version=pred_version)

    valid_cols = [col for col in dimension_lv2_fcst_group_by_cols if col in lv2_withdim_pred_df.columns]
    df = aggregate_pred_data(
        lv2_withdim_pred_df,
        valid_cols,
        lv2_withdim_pred_lst,
        []
    )

    df['period_id'] = pred_version

    save_to_local(df, 'lv2_withdim_pred')

    save_to_db(df, scenarios='LV2')
