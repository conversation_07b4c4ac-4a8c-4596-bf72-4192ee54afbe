# 业务服务重启问题处理方案

## 问题描述

在使用预测API服务时，偶尔会遇到"业务服务挂掉重启,errorcode:1405"的错误。这通常是由于外部预测服务的临时性重启或维护导致的。

## 问题分析

### 错误来源
- 错误信息来自外部预测API服务的响应
- 错误码1405表示业务服务正在重启
- 这是一个临时性问题，服务重启后通常会恢复正常

### 影响范围
- 轮询过程中的API调用失败
- 可能导致预测任务中断
- 影响整体预测流程的稳定性

## 解决方案

### 1. 增强错误检测和处理

#### 改进的API响应处理
- 检测响应中的错误码1405和"业务服务重启"关键词
- 区分临时性服务重启错误和其他类型的错误
- 对服务重启错误采用继续轮询策略，而不是直接失败

#### 代码改进位置
```python
# src/time_series_pred/services/profits_sequence_pred.py
def get_indicator_forecast(url, problemId, solutionId, version_id, dynamic_token):
    # 增加了对业务服务重启错误的特殊处理
    if "1405" in str(error_code) or "业务服务" in error_message or "重启" in error_message:
        log.warning(f"[业务服务重启] 检测到服务重启，将继续轮询等待服务恢复")
        return None  # 返回None而不是抛出异常
```

### 2. 智能轮询策略

#### 连续失败检测
- 添加连续失败计数器
- 当连续失败次数达到阈值时，自动检测可能的服务重启
- 动态调整轮询间隔

#### 服务重启检测
- 检测到服务重启时，延长等待时间
- 避免在服务重启期间频繁请求
- 提供更友好的日志信息

### 3. 动态Token刷新

#### Token过期处理
- 定期刷新动态token（默认5分钟）
- 防止因token过期导致的认证失败
- 自动重试机制

#### 配置参数
```json
{
  "FORCAST_API": {
    "max_consecutive_failures": 5,
    "token_refresh_interval": 300,
    "service_restart_wait_multiplier": 2,
    "max_service_restart_wait": 30
  }
}
```

### 4. 详细的日志记录

#### 增强的日志信息
- 区分不同类型的错误
- 记录服务重启检测状态
- 提供详细的轮询进度信息

#### 日志示例
```
[业务服务重启] versionId: 202510292033316, 检测到服务重启，将继续轮询等待服务恢复
[服务重启等待] versionId: 202510292033316, 第3次轮询, 检测到服务重启，延长等待时间至: 20.0秒
[轮询成功] versionId: 202510292033316, 第5次轮询成功, 服务已恢复正常
```

## 配置说明

### 新增配置参数

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| `max_consecutive_failures` | 5 | 连续失败的最大次数，超过后认为可能是服务重启 |
| `token_refresh_interval` | 300 | Token刷新间隔（秒） |
| `service_restart_wait_multiplier` | 2 | 服务重启时等待时间倍数 |
| `max_service_restart_wait` | 30 | 服务重启时最大等待时间（秒） |

### 配置文件位置
- `src/config/LOCAL.json`
- `src/config/SIT-AIF.json`
- `src/config/DEV-AIF.json`
- `src/config/PROD-AIF.json`

## 使用建议

### 1. 监控和告警
- 关注日志中的服务重启检测信息
- 设置适当的告警阈值
- 监控轮询成功率

### 2. 配置调优
- 根据实际环境调整配置参数
- 在高负载环境中可能需要增加等待时间
- 根据网络状况调整超时设置

### 3. 故障排除
- 检查网络连接状态
- 验证API服务状态
- 确认token有效性

## 预期效果

### 改进前
- 遇到服务重启错误时直接失败
- 需要手动重试整个预测流程
- 用户体验较差

### 改进后
- 自动检测和处理服务重启错误
- 智能等待服务恢复
- 提高预测流程的稳定性和成功率
- 减少人工干预需求

## 注意事项

1. **兼容性**: 改进后的代码向后兼容，不会影响现有功能
2. **性能**: 增加的检测逻辑对性能影响微乎其微
3. **可靠性**: 通过多层错误处理提高了系统的可靠性
4. **可维护性**: 通过配置参数可以灵活调整行为

## 测试建议

1. **正常场景测试**: 确保正常预测流程不受影响
2. **服务重启模拟**: 模拟服务重启场景验证处理逻辑
3. **长时间运行测试**: 验证token刷新机制的有效性
4. **高并发测试**: 确保在高负载下的稳定性
