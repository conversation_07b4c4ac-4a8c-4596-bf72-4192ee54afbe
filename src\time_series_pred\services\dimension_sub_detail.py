from pathlib import Path

import pandas as pd

from src.time_series_pred.common.dimension_constant import dimension_fcst_group_by_cols
from src.time_series_pred.common.dimension_constant import dimension_sub_detail_dim
from src.time_series_pred.db.get_data import get_data
from src.time_series_pred.db.save_to_db import save_to_db
from src.time_series_pred.services.process_after_pred import correct_cost_price_anomalies
from src.time_series_pred.services.process_after_pred import post_process_non_nagtive
from src.time_series_pred.services.profits_sequence_pred import integrate_results
from src.utils.resource_loader import CONFIG_LOADER
from src.utils.util import check_null_values, aggregate_pred_data

# 预测因子
dimension_pred_lst = ['unit_cost', 'unit_price', 'carryover_rate']

# 区间预测
dim_need_interval_prediction = ['unit_cost', 'unit_price']

cols_to_convert_numeric = ['equip_cost_cons_before_amt', 'unit_cost']


def profit_forecast(pred_version: int):
    # 获取密钥
    sql_path = Path(__file__).parent.parent / "sql"
    try:
        secret_key = CONFIG_LOADER.get_accounts_by_name('profits.gs.dataKey')['password']

        sql_path = sql_path / "dimension_sub_detail.sql"
        with open(sql_path, "r", encoding="utf-8") as f:
            sql_template = f.read()
        sql_query = sql_template.format(pred_version=pred_version, secret_key=secret_key)
        dimension_sub_detail_his_df = get_data(sql_query)
    except (KeyError, TypeError) as e:
        raise RuntimeError("Failed to retrieve secret key from configuration") from e
    except FileNotFoundError as e:
        raise FileNotFoundError(f"SQL file not found: {sql_path}") from e
    except Exception as e:
        raise RuntimeError(f"Database query failed: {str(e)}") from e

    dimension_sub_detail_his_df[cols_to_convert_numeric] = dimension_sub_detail_his_df[cols_to_convert_numeric].apply(
        pd.to_numeric, errors='coerce')

    # 检查空值
    check_null_values(dimension_sub_detail_his_df, dimension_pred_lst)

    if dimension_sub_detail_his_df.empty:
        raise ValueError("No valid data found")

    dimension_sub_detail_pred_df = integrate_results(
        his_df=dimension_sub_detail_his_df,
        dim=dimension_sub_detail_dim,
        pred_cols=dimension_pred_lst,
        period_col='target_period',
        pred_version=pred_version
    )

    valid_cols = [col for col in dimension_fcst_group_by_cols if col in dimension_sub_detail_pred_df.columns]
    df = aggregate_pred_data(
        dimension_sub_detail_pred_df,
        valid_cols,
        dimension_pred_lst,
        dim_need_interval_prediction
    )

    # 处理异常数据成本异常
    correct_cost_price_anomalies(df, dimension_sub_detail_his_df, dimension_sub_detail_dim)

    # 非负化处理
    df = post_process_non_nagtive(
        df=df,
        his_df=dimension_sub_detail_his_df,
        dim=dimension_sub_detail_dim,
        data_cols=dimension_pred_lst
    )

    df['period_id'] = pred_version

    save_to_db(df, scenarios='DIM', is_encrypt=True)
