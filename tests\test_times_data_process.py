import sys

import pandas as pd

from tests.conftest import mock_module

sys.modules["src.utils.resource_loader"] = mock_module


def test_aggregate_forecast_data_normal_case():
    from src.utils.util import aggregate_pred_data
    from src.time_series_pred.common.dimension_constant import dimension_fcst_group_by_cols

    input_df = pd.DataFrame({
        'scenarios': ['A', 'A', 'B', 'B'],
        'target_period': ['2023-01', '2023-01', '2023-01', '2023-02'],
        'unit_cost_fcst': [10.0, 15.0, 20.0, 25.0],
        'unit_price_fcst': [20.0, 25.0, 30.0, 35.0],
        'unit_cost_fcst_upper': [12.0, 16.0, 22.0, 27.0],
        'unit_cost_fcst_lower': [8.0, 14.0, 18.0, 23.0],
        'unit_cost_fcst_conf': [0.9, 0.85, 0.95, 0.88]
    })

    valid_cols = [col for col in dimension_fcst_group_by_cols if col in input_df.columns]
    result = aggregate_pred_data(
        df=input_df,
        group_by_cols=valid_cols,
        pred_cols=['unit_cost', 'unit_price'],
        interval_cols=['unit_cost']
    )

    # 验证结果形状
    assert len(result) == 3  # A-2023-01, B-2023-01, B-2023-02
    assert list(result.columns) == ['scenarios', 'target_period',
                                    'unit_cost_fcst', 'unit_price_fcst',
                                    'unit_cost_fcst_upper', 'unit_cost_fcst_lower', 'unit_cost_fcst_conf']

    a_2023_01_row = result[(result['scenarios'] == 'A') & (result['target_period'] == '2023-01')]
    assert a_2023_01_row['unit_cost_fcst'].iloc[0] == 15.0  # max(10, 15)
    assert a_2023_01_row['unit_price_fcst'].iloc[0] == 25.0  # max(20, 25)