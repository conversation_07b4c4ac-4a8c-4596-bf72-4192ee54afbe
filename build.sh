#！/bin/bash
set -ex
#获取jinkins的$buildNumber，获取时间戳，获取build随机数
if [ -z ${buildNumber} ];then
    if [ -e /proc/sys/kernel/random/uuid ] && [ -r /proc/sys/kernel/random/uuid ];then
        build=`cat /proc/sys/kernel/random/uuid| cksum | cut -f1 -d" "`
    else
        build=${RANDOM}
    fi
    datetime=`date +%Y%m%d%H%M%S`
    buildNumber="${datetime}.${build}"
else
    buildNumber="${buildNumber}"
fi


#微服务名称
SERVICE_NAME="OptFcstProfitsModel"
#包名称
PACKAGE_NAME="opt_fcst_profits_model-online"

echo "Release is ${isRelease}"
#判断当前构建是否为版本构建，以及定义构建变量(包版本,包服务名称,包编译存放路径,包类型,包编译名称,包打包名称)
if [ "${isRelease}"x = "false"x ];then
    SERVICE_VERSION='1.0.0-SNAPSHOT'
	  #版本号+时间戳+build随机数写入buildInfo.properties
	  echo "buildVersion=${SERVICE_VERSION}.$buildNumber">buildInfo.properties
	  #sed -i 's/VERSION/'${SERVICE_VERSION}.${buildNumber}'/g' appspec.yml
	  #压缩包名称
	  PACKAGE_TAR_PATH="${SERVICE_NAME}_${SERVICE_VERSION}.${buildNumber}"
	  #执行工程编译
	  workdir=$(cd $(dirname $0); pwd)
	  pip3 config set global.index-url https://cmc.centralrepo.rnd.huawei.com/artifactory/pypi-central-repo/simple/
	  rm -rf ${PACKAGE_NAME}/*
      pwd
    tar -zcvf opt_fcst_profits_model-online.tar.gz .[!.]* *
	  #maven打包命令
	#   mvn clean package -Dmaven.test.skip=true -P!dev,online -f pom.xml
elif [ "${isRelease}"x = "true"x ];then
    SERVICE_VERSION=${releaseVersion}
	  #版本号+时间戳+build随机数写入buildInfo.properties
	  echo "buildVersion=${SERVICE_VERSION}">buildInfo.properties
	  #sed -i 's/VERSION/'${SERVICE_VERSION}'/g' appspec.yml
	  #压缩包名称
	  PACKAGE_TAR_PATH="${SERVICE_NAME}_${SERVICE_VERSION}"
	  #执行工程编译
	  workdir=$(cd $(dirname $0); pwd)
	  pip3 config set global.index-url https://cmc.centralrepo.rnd.huawei.com/artifactory/pypi-central-repo/simple/
	  rm -rf ${PACKAGE_NAME}/*
	  pwd
	  #Maven打包命令
    # yum install tar
    tar -zcvf opt_fcst_profits_model-online.tar.gz .[!.]* *
    # mvn clean package -Dmaven.test.skip=true -P!dev,online -f pom.xml #以上为案例，请产品团队自行修改

fi