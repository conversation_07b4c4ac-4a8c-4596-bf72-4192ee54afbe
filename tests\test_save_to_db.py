import sys
from unittest.mock import Mock

import numpy as np
import pandas as pd
import pytest
from sqlalchemy.orm import Session

from tests.conftest import mock_module

sys.modules["src.utils.resource_loader"] = mock_module

from src.utils.resource_loader import RDB_POOL
from src.time_series_pred.db.save_to_db import insert_to_database_with_encrypt


# 空数据处理
def test_insert_empty_dataframe():
    # 设置mock session
    mock_session = Mock(spec=Session)
    # 正确设置context manager mock
    context_manager = Mock()
    context_manager.__enter__ = Mock(return_value=mock_session)
    context_manager.__exit__ = Mock(return_value=None)
    RDB_POOL.context_session.return_value = context_manager

    insert_to_database_with_encrypt(pd.DataFrame(), 'dm_fop_dimension_fcst_t', 'abc123')

    # 验证没有执行SQL（因为数据为空）
    mock_session.execute.assert_not_called()


# NaN 值处理 (unit_cost_fcst_encrypt 为 NULL)
def test_nan_handling():
    mock_session = Mock(spec=Session)
    context_manager = Mock()
    context_manager.__enter__ = Mock(return_value=mock_session)
    context_manager.__exit__ = Mock(return_value=None)
    RDB_POOL.context_session.return_value = context_manager

    test_df = pd.DataFrame({
        'unit_cost_fcst': [10.5, np.nan, 123, 20.75],
        'period_id': [202509, 202509, 202510, 202510],
        'lv1_code': ['Product A', "O'Brien's", 'Product C', 'Product D']
    })

    insert_to_database_with_encrypt(test_df, 'dm_fop_dimension_fcst_t', 'abc123')

    mock_session.execute.assert_called_once()

    actual_sql = mock_session.execute.call_args[0][0]

    expected_sql = '''
    INSERT INTO "dm_fop_dimension_fcst_t" (unit_cost_fcst_encrypt, period_id, lv1_code) VALUES 
    (gs_encrypt(10.5, 'abc123', 'aes128', 'cbc', 'sha256'), 202509, 'Product A'),
    (NULL, 202509, 'O''Brien''s'),
    (gs_encrypt(123.0, 'abc123', 'aes128', 'cbc', 'sha256'), 202510, 'Product C'),
    (gs_encrypt(20.75, 'abc123', 'aes128', 'cbc', 'sha256'), 202510, 'Product D')
    '''

    assert str(actual_sql).replace(' ', '').replace('\n', '') == str(expected_sql).replace(' ', '').replace('\n', '')


# 数值字段处理
def test_numeric_handling():
    mock_session = Mock(spec=Session)
    context_manager = Mock()
    context_manager.__enter__ = Mock(return_value=mock_session)
    context_manager.__exit__ = Mock(return_value=None)
    RDB_POOL.context_session.return_value = context_manager

    test_df = pd.DataFrame({
        'unit_cost_fcst': [10.5, np.nan, 'abc', 20.75],
        'period_id': [202509, 202509, 202510, 202510],
        'lv1_code': ['Product A', "O'Brien's", 'Product C', 'Product D']
    })
    insert_to_database_with_encrypt(test_df, 'dm_fop_dimension_fcst_t', 'abc123')

    actual_sql = str(mock_session.execute.call_args[0][0])

    # 检查数值字段是否被正确处理
    assert "gs_encrypt(10.5, 'abc123', 'aes128', 'cbc', 'sha256')" in actual_sql
    assert "gs_encrypt(20.75, 'abc123', 'aes128', 'cbc', 'sha256')" in actual_sql
    assert "gs_encrypt(abc, 'abc123', 'aes128', 'cbc', 'sha256')" in actual_sql


# 异常处理验证
def test_exception_handling():
    mock_session = Mock(spec=Session)
    context_manager = Mock()
    context_manager.__enter__ = Mock(return_value=mock_session)
    context_manager.__exit__ = Mock(return_value=None)
    RDB_POOL.context_session.return_value = context_manager
    mock_session.execute.side_effect = Exception("DB Error")

    test_df = pd.DataFrame({
        'unit_cost_fcst': [10.5, np.nan, 'abc', 20.75],
        'period_id': [202509, 202509, 202510, 202510],
        'lv1_code': ['Product A', "O'Brien's", 'Product C', 'Product D']
    })

    with pytest.raises(Exception) as exc_info:
        insert_to_database_with_encrypt(test_df, 'dm_fop_dimension_fcst_t', 'abc123')

    mock_session.rollback.assert_called_once()
    assert "DB Error" in str(exc_info.value)


def test_sql_generation_logic():
    mock_session = Mock(spec=Session)
    context_manager = Mock()
    context_manager.__enter__ = Mock(return_value=mock_session)
    context_manager.__exit__ = Mock(return_value=None)
    RDB_POOL.context_session.return_value = context_manager

    test_df = pd.DataFrame({
        'period_id': [202401, 202402],
        'bg_code': ['BG001', 'BG002'],
        'bg_name': ['业务组1', '业务组2'],
        'unit_cost_fcst': [100.5, 200.75],
        'unit_price_fcst': [150.0, 250.0],
        'currency': ['CNY', 'CNY'],
        'created_by': [-1, -1],
        'creation_date': ['2024-01-01 10:00:00', '2024-01-01 10:00:00']
    })
    insert_to_database_with_encrypt(test_df, 'dm_fop_dimension_fcst_t', 'abc123')

    mock_session.execute.assert_called_once()

    actual_sql = mock_session.execute.call_args[0][0]
    sql_str = str(actual_sql)

    assert 'INSERT INTO "dm_fop_dimension_fcst_t"' in sql_str
    assert 'unit_cost_fcst_encrypt' in sql_str
    assert 'gs_encrypt(' in sql_str
    assert 'BG001' in sql_str
    assert '业务组1' in sql_str