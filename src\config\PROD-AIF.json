{"DEPLOY": {"cloud": "HIS", "infra": "AIF", "enable_doc": false, "service_name": "", "uri_prefix": "/lg-tspred", "config_server": {"url": "https://appconfig.huawei.com/ConfigCenter/services/saasConfigcenterGetConfig", "app_id": "com.huawei.finance.ai.opt.fop", "du": "fcst_profits_service", "environment": "pro", "region": "cn-south-1", "version": "1.0", "config_parts": ["5c02b1f044e4479eb08b283eabecfa17", "966214e4afcc45bab319478f317ee14b"]}, "api_gateway": {"authorization": {"url": "https://w3cloud.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken"}, "authentication": {"sgov_token": "https://w3cloud.huawei.com/ApiCommonQuery/appToken/validateToken", "sgov_uri": "https://w3cloud.huawei.com/ApiCommonQuery/appToken/authorizeByURI"}}}, "LOGGER": {"level": "INFO"}, "RDB": {"default": {"datasource_name": "fcst_pro", "tables": ["dm_fop_ict_pl_sum_t"], "echo": false}}, "LIMITER": {"storage_uri": null, "strategy": "moving-window", "unit": "minute", "frequency": 10}, "APP_CONFIG": {"logging_max_body_length": 1024, "logging_separator": "\n", "logging_custom_headers": ["X-TENANTID", "X-APPID", "X-USERID"]}, "FORCAST_API": {"upload_url": "https://ai.finance.huawei.com/opt/pc/ifs/v1/version/createVersionStartForecastByFile", "forecast_result_url": "https://ai.finance.huawei.com/opt/pc/ifs/v1/version/generateTemporaryURL", "referer": "https://ai.finance.huawei.com/opt/ifs/portal", "request_max_timeout": 10, "max_duration": 900, "interval": 10, "max_consecutive_failures": 5, "token_refresh_interval": 300, "service_restart_wait_multiplier": 2, "max_service_restart_wait": 30}}