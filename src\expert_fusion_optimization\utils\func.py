import time
import warnings
warnings.filterwarnings('ignore')


def get_other_columns(df):
    """构造存入数据库的其他标记列

    Parameters
    ----------
    df: 预测结果长表

    Returns
    -------
    dataframe
    """
    df["remark"] = None
    df["created_by"] = -1
    df["creation_date"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    df["last_updated_by"] = -1
    df["last_update_date"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    df["del_flag"] = "N"
    df["currency"] = 'CNY'
    df["fcst_type"] = 'YTD法'
    df['aggregate_flag'] = None
    return df
