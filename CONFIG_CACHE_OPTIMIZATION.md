# 配置中心缓存优化方案

## 问题分析

### 原始问题
在 `his_config_center_client.py` 中发现了重复调用的问题：

1. **`get_application_config_by_key` 重复调用 `get_application_config`**
   - 每次调用都会重新创建 DataFrame 并进行列名转换
   - 虽然 `get_all_config` 函数已有 TTL 缓存，但数据处理部分没有缓存

2. **`escape_switch_enabled` 配置开关频繁查询**
   - 在 `use_sdk_for_prediction` 函数中每次都调用配置中心
   - 这种开关类配置通常不需要实时更新，适合缓存

## 优化方案

### 1. 实例级别缓存优化

**文件**: `pyxis/utils/config_center/his_config_center_client.py`

**优化内容**:
- 为 `HisConfigCenterClient` 类添加实例级别的缓存变量
- 缓存已处理的 DataFrame，避免重复的数据处理操作
- 保留原有的 `get_all_config` 函数级别缓存

**缓存的配置类型**:
```python
self._account_config_cache: Optional[pd.DataFrame] = None
self._datasource_config_cache: Optional[pd.DataFrame] = None
self._application_config_cache: Optional[pd.DataFrame] = None
self._distributed_storage_config_cache: Optional[pd.DataFrame] = None
self._decrypted_accounts_cache: Optional[pd.DataFrame] = None
self._decrypted_datasource_cache: Optional[pd.DataFrame] = None
self._decrypted_distributed_storage_cache: Optional[pd.DataFrame] = None
```

**优化效果**:
- 减少重复的 DataFrame 创建和列名转换操作
- `get_application_config_by_key` 多次调用时只处理数据一次
- 保持 API 兼容性，无需修改调用代码

### 2. 全局配置开关缓存

**文件**: `src/utils/resource_loader.py`

**新增内容**:
- `ConfigSwitches` 单例类，在应用启动时初始化
- 缓存常用的开关配置，避免重复查询配置中心

**使用方式**:
```python
# 原始方式（每次都查询配置中心）
escape_switch_enabled = CONFIG_LOADER.get_application_config_by_key("escape_switch_enabled") == 'true'

# 优化后方式（使用缓存）
from src.utils.resource_loader import CONFIG_SWITCHES
if CONFIG_SWITCHES.escape_switch_enabled:
    return True
```

**优化效果**:
- 将配置查询从 O(n) 降低到 O(1)
- 减少网络请求和配置中心负载
- 特别适合频繁调用的场景

### 3. 配置管理接口

**文件**: `src/router/config_management.py`

**提供的接口**:
- `GET /config/switches` - 获取当前配置开关状态
- `POST /config/switches/reload` - 重新加载配置开关
- `GET /config/cache/status` - 获取缓存状态信息

**用途**:
- 运行时查看配置状态
- 配置变更后手动刷新缓存
- 监控缓存使用情况

## 性能提升分析

### 1. 网络请求优化

**原始方式**:
- 每次 `get_application_config_by_key` 调用 → 可能触发网络请求
- 频繁调用场景下网络开销显著

**优化后**:
- 应用启动时查询一次，后续使用缓存
- 网络请求次数从 O(n) 降低到 O(1)

### 2. 数据处理优化

**原始方式**:
- 每次调用都创建 DataFrame 并转换列名
- CPU 和内存开销随调用次数线性增长

**优化后**:
- DataFrame 创建和处理只执行一次
- 后续调用直接返回缓存的对象

### 3. 实际场景效果

**高频调用场景**:
- 预测任务中 `use_sdk_for_prediction` 可能被调用数百次
- 每次调用节省的时间累积效果显著
- 减少配置中心负载，提升系统稳定性

## 使用指南

### 1. 配置开关缓存

**添加新的配置开关**:
```python
# 在 ConfigSwitches._load_switches() 方法中添加
def _load_switches(self):
    self.escape_switch_enabled = CONFIG_LOADER.get_application_config_by_key("escape_switch_enabled") == 'true'
    self.new_switch = CONFIG_LOADER.get_application_config_by_key("new_switch") == 'true'
```

**使用配置开关**:
```python
from src.utils.resource_loader import CONFIG_SWITCHES

def some_function():
    if CONFIG_SWITCHES.escape_switch_enabled:
        # 执行相应逻辑
        pass
```

### 2. 运行时配置更新

**手动刷新配置**:
```bash
# 重新加载配置开关
curl -X POST "http://localhost:8000/config/switches/reload"

# 查看当前配置状态
curl "http://localhost:8000/config/switches"
```

### 3. 监控和调试

**查看缓存状态**:
```bash
curl "http://localhost:8000/config/cache/status"
```

**日志监控**:
- 应用启动时会记录配置开关的初始值
- 配置重新加载时会记录变更情况

## 注意事项

### 1. 缓存一致性

- 配置开关缓存适用于不需要实时更新的配置
- 如需实时性，可以通过管理接口手动刷新
- 考虑添加定时刷新机制（如需要）

### 2. 内存使用

- 实例级别缓存会占用一定内存
- DataFrame 缓存的内存占用取决于配置数据量
- 通常配置数据量不大，内存影响可忽略

### 3. 线程安全

- 当前实现适用于单线程场景
- 如需多线程支持，考虑添加锁机制

## 扩展建议

### 1. 定时刷新

可以考虑添加定时刷新机制：
```python
import asyncio

async def periodic_config_refresh():
    while True:
        await asyncio.sleep(300)  # 5分钟刷新一次
        CONFIG_SWITCHES.reload_switches()
```

### 2. 配置变更通知

可以考虑集成配置中心的变更通知机制，实现配置的主动推送更新。

### 3. 更多配置类型

可以扩展缓存机制到其他类型的配置，如数值型配置、字符串配置等。

## 总结

通过实例级别缓存和全局配置开关缓存的双重优化：

1. **显著减少网络请求次数**
2. **避免重复的数据处理操作**
3. **提升高频调用场景的性能**
4. **降低配置中心负载**
5. **保持 API 兼容性**
6. **提供运行时配置管理能力**

这种优化方案在保持代码简洁性的同时，有效解决了配置重复调用的性能问题。
