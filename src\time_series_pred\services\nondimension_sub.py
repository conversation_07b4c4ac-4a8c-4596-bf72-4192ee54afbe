from pathlib import Path

from src.time_series_pred.common.dimension_constant import dimension_subcategory_dim, dimension_fcst_group_by_cols
from src.time_series_pred.db.get_data import get_data
from src.time_series_pred.db.save_to_db import save_to_db
from src.time_series_pred.services.process_after_pred import after_process_ratio
from src.time_series_pred.services.profits_sequence_pred import integrate_results
from src.utils.util import check_null_values, aggregate_pred_data, save_to_local

# 预测因子
nondimension_pred_lst = ['rev_percent', 'mgp_ratio']
# 区间预测
dim_need_interval_prediction = ['unit_cost', 'unit_price']
nondim_need_interval_prediction = ['mgp_ratio']
cols_to_check = dim_need_interval_prediction+nondim_need_interval_prediction


def profit_forecast(pred_version: int):
    try:
        sql_path = Path(__file__).parent.parent / "sql" / "nondimension_sub.sql"
        with open(sql_path, "r", encoding="utf-8") as f:
            sql_template = f.read()
        sql_query = sql_template.format(pred_version=pred_version)
        df = get_data(sql_query)
    except Exception as e:
        raise RuntimeError(f"Database query failed: {str(e)}") from e

    # 检查结果
    check_null_values(df, cols_to_check)

    # 无量纲子类
    nondimension_subcategory_his_df = df[
        (df['scenarios'] == '量纲子类') & (df['dimension_subcategory_code'] == 'NOSUB')]

    nondimension_subcategory_pred_df = integrate_results(his_df=nondimension_subcategory_his_df,
                                                         dim=dimension_subcategory_dim, pred_cols=nondimension_pred_lst,
                                                         period_col='target_period', pred_version=pred_version)

    valid_cols = [col for col in dimension_fcst_group_by_cols if col in nondimension_subcategory_pred_df.columns]
    df = aggregate_pred_data(
        nondimension_subcategory_pred_df,
        valid_cols,
        nondimension_pred_lst,
        nondim_need_interval_prediction
    )

    df['period_id'] = pred_version
    after_process_ratio(df, nondimension_subcategory_his_df, dimension_subcategory_dim, his_param='mgp_ratio_ytd_data',
                        pre_param='mgp_ratio_fcst')

    save_to_local(df, 'nondimension_subcategory_pred')

    save_to_db(df, scenarios='DIM')

