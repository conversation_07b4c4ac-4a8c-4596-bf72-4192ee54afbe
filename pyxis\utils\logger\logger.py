import logging
import os
from logging.handlers import RotatingFileHand<PERSON>


def get_base_logger(
    name: str = "Logger",
    output_directory: str = None,
    log_to_file: bool = False,
    log_format: str = ("%(asctime)s | PID-%(process)d-%(threadName)s-%(thread)d | "
                       "%(name)s | %(filename)s:%(lineno)d | %(levelname)s | %(message)s"),
    level: str = "DEBUG",
    max_file_size: int = 1024 * 1024 * 10,
    backup_count: int = 5,
    disable_propagation: bool = False
) -> logging.Logger:
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level))  # Set level from string

    formatter = logging.Formatter(log_format)

    # Clear existing handlers to prevent duplicate logs
    logger.handlers.clear()

    # Disable propagation if requested (useful in AIF environments)
    if disable_propagation:
        logger.propagate = False

    if log_to_file and output_directory:
        log_dir = os.path.dirname(output_directory)
        if not os.path.exists(log_dir):
            os.mkdir(log_dir)
        file_handler = RotatingFileHandler(
            filename=output_directory,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding="utf-8"
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger
