#!/use/bin/env python
# -*- coding: utf-8 -*-
# 版权所有©华为技术有限公司 2025-2030
"""
解密配置中心配置
"""

import hashlib
import os
import stat
from abc import ABC, abstractmethod
from pathlib import Path

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes


class Constant(object):
    PATH = r"/opt/security/%(app_short_name)s/%(sub_app_id)s/%(deploy_env)s/keys/rootkeys"
    KEY_LIST = ('.key', '.KEY')
    PAYLOAD = "payload="
    SHA256 = "sha256"
    BYTE_LEN = 16
    BYTE_LEN_32 = 32
    COUNT = 10000
    TILDE = "~"
    A2 = 'A2'
    SALT_ONE_ENV = "J2C_SALT_ONE"  # 盐值1的环境变量名，按照这个名字去查找当前环境变量值
    SALT_TWO_ENV = "J2C_SALT_TWO"  # 盐值2的环境变量名
    MAIN_KEY = "main_key"  # 默认主根环境变量名
    ASSIST_KEY = "assist_key"  # 默认辅根环境变量名


class EncryptType:
    ADV_2_5 = "ADVANCED2.5"
    ADV_2_6 = "ADVANCED2.6"


class AESCrypto(object):
    """
    AES加解密：基础类
    """
    def __init__(self, key, iv):
        self.AES_GCM_KEY = key
        self.AES_GCM_IV = iv

    def encrypt(self, data, mode='gcm'):
        func_name = "{}_encrypt".format(mode)
        func = getattr(self, func_name)
        return func(data)

    def decrypt(self, data, mode='gcm'):
        func_name = "{}_decrypt".format(mode)
        func = getattr(self, func_name)
        return func(data)

    @staticmethod
    def pkcs7_padding(data):
        if not isinstance(data, bytes):
            data = data.encode()

        padder = padding.PKCS7(algorithms.AES.block_size).padder()
        padded_data = padder.update(data) + padder.finalize()

        return padded_data

    def gcm_encrypt(self, data):
        if not isinstance(data, bytes):
            data = data.encode()

        cipher = Cipher(algorithms.AES(self.AES_GCM_KEY),
                        modes.GCM(self.AES_GCM_IV),
                        backend=default_backend())
        encryptor = cipher.encryptor()
        padded_data = encryptor.update(self.pkcs7_padding(data))

        return padded_data

    def gcm_decrypt(self, data):
        if not isinstance(data, bytes):
            data = data.encode()
        try:
            cipher = Cipher(algorithms.AES(self.AES_GCM_KEY),
                            modes.GCM(self.AES_GCM_IV),
                            backend=default_backend())
            decryptor = cipher.decryptor()
            uppaded_data = self.pkcs7_unpadding(decryptor.update(data))
            uppaded_data = uppaded_data.decode()

            return uppaded_data
        except Exception as e:
            raise ValueError("Please check if the primary key and secondary key are correct.") from e
        finally:
            pass

    @staticmethod
    def pkcs7_unpadding(padded_data):
        data = padded_data[:len(padded_data) - Constant.BYTE_LEN]
        return data


class HisDecrypt(object):
    """
    Real implementation
    """

    def __init__(self, version=25):
        """
        init
        :param version
        """
        self._key_loaders = list()
        self.version = version

    @staticmethod
    def calculate(part_list):
        """
        用config_parts以及主根密钥和辅根密钥
        计算生成16位的bytearray
        :return: bytearray
        """
        if len(part_list) != 2:
            raise ValueError("config_parts length must be 2!")
        p1, p2 = part_list[0], part_list[1]
        part1 = p1 if isinstance(p1, bytearray) else bytearray(
            p1.encode('utf-8'))
        part2 = p2 if isinstance(p2, bytearray) else bytearray(
            p2.encode('utf-8'))
        if len(part1) > len(part2):
            long_bytes = part1
            short_bytes = part2
        else:
            long_bytes = part2
            short_bytes = part1
        orbyte = bytearray(len(long_bytes))

        i = 0
        for j, _ in enumerate(short_bytes):
            orbyte[j] = short_bytes[j] ^ long_bytes[j]
            i += 1
        while i < len(long_bytes):
            orbyte[i] = long_bytes[i]
            i += 1

        return orbyte

    @staticmethod
    def gen_key_by_pbkdf2_hmac(sec_key, salt_array, byte_length):
        """
        使用pbkdf2_hmac方法，通过sha256算法
        对秘钥进行加密，输出加密后16位的字节数组
        :param sec_key: 秘钥
        :param salt_array: 盐值
        :param byte_length: length of bytes
        :return:
        """
        salt = bytearray(salt_array)
        count = Constant.COUNT

        secret_key = hashlib.pbkdf2_hmac(
            Constant.SHA256, sec_key, salt, count, byte_length
        )
        return secret_key

    def _decrypt_by_aes(self, key, salt_array, iv, byte_len, text=None):
        """
        使用AES对密钥进行解密，密钥长度需为16的倍数
        :param key: 秘钥
        :param salt_array: 盐值
        :param iv: 偏移量
        :param byte_len: version of encrypt
        :param text: 明文
        :return:
        """
        secret_key = self.gen_key_by_pbkdf2_hmac(key, salt_array, byte_len)
        aes_crypto = AESCrypto(secret_key, iv)
        decrypt_text = aes_crypto.decrypt(text)
        return decrypt_text

    @staticmethod
    def parse_hexstr2byte(hex_str):
        """
        将16进制字符串转为字节数组
        :param hex_str:
        :return: bytearray
        """
        try:
            return bytes.fromhex(hex_str)
        except Exception as e:
            raise ValueError("The ciphertext of the work_key_cipher or password is incorrect") from e
        finally:
            pass

    @staticmethod
    def parse_byte2hexstr(byte_arr):
        """
        将pbkdf2_hmac算法加密返回的16位
        字节数组转换为16进制的字符串
        :param byte_arr: bytearray
        :return: string
        """
        hex_str = byte_arr.hex().upper()
        return hex_str

    def _get_secretkey_by_aes(self, secret_key, cipher_key, encrypt_type):
        """
        准备AES解密所需的秘钥，明文，模式和IV偏移量
        :param secret_key: 解密所用的密钥
        :param cipher_key: 需解密的明文
        :param encrypt_type: version of j2c(2.5/2.6)
        :return:
        """
        cipher_key_list = cipher_key.split(Constant.TILDE)
        if cipher_key_list[1] != Constant.A2:
            raise ValueError("No A2 in the {}".format(cipher_key))

        byte_len = Constant.BYTE_LEN
        salt_array = None
        if encrypt_type == EncryptType.ADV_2_5:
            byte_len = Constant.BYTE_LEN
            env_salt_two = os.environ.get(Constant.SALT_TWO_ENV)
            if not env_salt_two:
                raise ModuleNotFoundError("J2C SALT 2 not found in environ.")
            salt_array = [int(i) for i in env_salt_two.strip().split(",")]
        elif encrypt_type == EncryptType.ADV_2_6:
            byte_len = Constant.BYTE_LEN_32
            salt_array = self.parse_hexstr2byte(cipher_key_list[-1])

        if not salt_array:
            raise ValueError("salt_array is none.")

        iv = self.parse_hexstr2byte(cipher_key_list[2])
        text = self.parse_hexstr2byte(cipher_key_list[3])
        decrypt_key = self._decrypt_by_aes(
            secret_key.encode('utf-8'), salt_array, iv, byte_len, text
        )
        return decrypt_key

    def register(self, key_loader):
        """
        将key_loader对象加入到列表中
        :param key_loader: KeyLoader对象
        :return:
        """
        self._key_loaders.append(key_loader)

    def decrypt(self, config_parts, work_key_cipher, password,
                encrypt_type=EncryptType.ADV_2_5):
        """
        解密
        :param config_parts: config_parts
        :param work_key_cipher: work_key_cipher
        :param password: password
        :param encrypt_type: j2c encrypt type
               like ['ADVANCED2.5', 'ADVANCED2.6']
        :return: decrypt password
        """
        key_list = list()
        for kl in self._key_loaders:
            if kl:
                key_list = kl.load_key()
                break
        if not key_list:
            raise ValueError("Get no key_list, make sure you have register KeyLoader.")

        key = self.calculate(config_parts)
        for part in key_list:
            key = self.calculate([key, part])

        env_salt_one = os.environ.get(Constant.SALT_ONE_ENV)
        if not env_salt_one:
            raise ModuleNotFoundError("J2C SALT 1 not found in environ.")
        salt_array = [int(i) for i in env_salt_one.strip().split(",")]
        secret_key = self.gen_key_by_pbkdf2_hmac(key, salt_array,
                                                 Constant.BYTE_LEN)
        root_key = self.parse_byte2hexstr(secret_key)
        work_key = self._get_secretkey_by_aes(root_key, work_key_cipher,
                                              encrypt_type)
        decrypt_pwd = self._get_secretkey_by_aes(work_key, password,
                                                 encrypt_type)
        return decrypt_pwd


class KeyLoader(ABC):
    """
    秘钥加载抽象类
    """

    @abstractmethod
    def load_key(self):
        """
        加载秘钥抽象接口，需要子类实现
        :return: byte list
        """
        pass


class FileKeyLoader(KeyLoader):
    """
    从文件中加载秘钥
    """

    def __init__(self, file_path=None):
        """
        :param file_path: key文件所在路径全名
        """
        self.__key_path = file_path

    def load_key(self):
        """
        从指定路径下读取后缀为.key或.KEY的文件，获取payload属性
        :return: list
        """
        if not self.__key_path:
            raise FileNotFoundError(f"path not exist, path: {self.__key_path}")
        try:
            byte_list = list()
            for file in os.listdir(self.__key_path):
                if not file.endswith(Constant.KEY_LIST):
                    continue
                file_full_path = os.path.join(self.__key_path, file)
                FileKeyLoader.read_one_file_bytes(file_full_path, byte_list)
            if len(byte_list) != 2:
                raise ValueError("FileKeyLoader::must have 2 attributes, "
                                 "and start with 'payload='")
            return byte_list
        except FileNotFoundError as e:
            raise FileNotFoundError(f"FileKeyLoader::path or files not exist. path: {self.__key_path}") from e
        finally:
            pass

    @staticmethod
    def read_one_file_bytes(file_full_path, byte_list):
        """
        每个文件单独读取，减少代码复杂度
        @param file_full_path: 文件全路径
        @param byte_list:
        @return:
        """
        with os.fdopen(os.open(file_full_path,
                               os.O_RDONLY,
                               stat.S_IRWXU)) as fd:
            for line in fd.readlines():
                if not line.startswith(Constant.PAYLOAD):
                    continue

                part = line.split(Constant.PAYLOAD)[1].rstrip("\n")
                byte_list.append(bytearray(part.encode('utf-8')))


class ADSKeyLoader(FileKeyLoader):
    """
    从ADS 文件加载Key
    """

    def __init__(self, app_short_name, sub_app_id, deploy_env):
        """
        :param app_short_name: 应用简称
        :param sub_app_id: 部署单元
        :param deploy_env: 部署环境类型
        """
        self._app_short_name = app_short_name
        self._sub_app_id = sub_app_id
        self._deploy_env = deploy_env

        # 组装key文件路径
        key_path = self._assemble_key_path()
        super(ADSKeyLoader, self).__init__(key_path)

    def _assemble_key_path(self):
        """
        按照ADS的规则拼接key文件路径
        :return:
        """
        path = Constant.PATH % {
            "app_short_name": self._app_short_name,
            "sub_app_id": self._sub_app_id,
            "deploy_env": self._deploy_env
        }
        key_path = Path(path)
        if not key_path.is_dir():
            raise NotADirectoryError("ADSKeyLoader::key path does not exists.")
        return key_path


class EnvironKeyLoader(KeyLoader):
    """
    从环境变量读取Key
    """

    def __init__(self, main_key_var=Constant.MAIN_KEY,
                 assist_key_var=Constant.ASSIST_KEY):
        """
        初始化需传入指定的主/辅根环境变量名
        :param main_key_var: 主根环境变量名
        :param assist_key_var: 辅根环境变量名
        """
        self._main_key_var = main_key_var
        self._assist_key_var = assist_key_var

    def load_key(self):
        """
        直接从环境变量中获取主/辅根
        :return: byte list
        """
        main_key = os.environ.get(self._main_key_var, None)
        assist_key = os.environ.get(self._assist_key_var, None)
        if not main_key or not assist_key:
            raise ModuleNotFoundError(f"EnvironKeyLoader::environment "
                                      f"var [{self._main_key_var},"
                                      f" {self._assist_key_var}] not found.")
        byte_list = list()
        byte_list.append(bytearray(main_key.encode('utf-8')))
        byte_list.append(bytearray(assist_key.encode('utf-8')))
        return byte_list

