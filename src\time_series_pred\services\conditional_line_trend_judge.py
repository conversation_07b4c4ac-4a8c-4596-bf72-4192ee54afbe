import numpy as np
import pandas as pd

def check_param_type(name, value, allow_types, allow_null=False):
    """检查参数类型"""
    if allow_null and value is None:
        return
    if not isinstance(value, tuple(allow_types)):
        raise TypeError(f"参数 {name} 必须是 {allow_types} 类型")


def check_param_is_between(name, value, range_):
    """检查参数值范围"""
    if not (range_[0] <= value <= range_[1]):
        raise ValueError(f"参数 {name} 必须在 {range_[0]} 和 {range_[1]} 之间")


def ma_method(series, data_col, window=2):
    """
    生成指定窗口长度的滑动平均结果

    参数:
    series: pd.Series - 时序数据
    data_col: str - 生成结果的数据列列名
    window: int = 2 - 窗口长度

    返回:
    pd.DataFrame - 指定窗口长度的滑动平均数据
    """
    check_param_type("series", series, allow_types=[pd.Series], allow_null=False)
    check_param_is_between("window", window, [1, series.size])

    val_list = series.values.tolist()
    new_list = []
    for i in range(len(val_list) - window):
        new_list.append(np.mean(val_list[i: i + window]))
    return pd.DataFrame(new_list, columns=[data_col])


def line_trend_test(df: pd.DataFrame,
                    thres: float = 0.7,
                    length: int = 24):
    """
    检查时间序列是否有线性外推趋势

    参数:
    df: pd.DataFrame - 时间序列
    data_col: str - 数据列列名
    thres: float = 0.7 - 检测阈值(0,1)
    length: int = 24 - 检查长度

    返回:
    bool - 时间序列是否有线性外推趋势
    """
    data_col = "Data"
    data = ma_method(df[data_col], data_col)

    if data.shape[0] <= length:
        diff = data[data_col].diff()
    else:
        diff = data[data_col].iloc[-length:].diff()

    diff_shape = diff.shape[0] - 1
    trend_num1 = sum(diff > 0)
    trend_num2 = sum(diff < 0)
    thres_num = diff_shape * thres

    return trend_num1 > thres_num or trend_num2 > thres_num


def conditional_line_trend(df: pd.DataFrame,
                           line_trend_thres: float = 0.7):
    """
    条件线性外推趋势判断

    参数:
    df: 包含时间序列数据和data的对象
    data_col: str - 数据列名: Data
    judge_reject_tags: 传入可参考性判断的result_tag,如果有内容，说明拒绝检测,直接返回False
    line_trend_thres: float 线性外推趋势阈值

    返回:
    bool - 是否有线性外推趋势
    """
    return line_trend_test(df, thres=line_trend_thres)
