SELECT
    version_code,
    scenarios,
    time_window_code,
    period_id,
    target_period,
    bg_code,
    bg_name,
    oversea_code,
    oversea_desc,
    lv1_code,
    lv1_name,
    lv2_code,
    lv2_name,
    dimension_group_code,
    dimension_group_cn_name,
    dimension_group_en_name,
    dimension_subcategory_code,
    dimension_subcategory_cn_name,
    dimension_subcategory_en_name,
    currency,
    equip_rev_cons_before_amt,
    CAST( gs_decrypt(equip_cost_amt_encrypt, '{secret_key}', 'AES128', 'CBC', 'SHA256') AS NUMERIC ) AS equip_cost_cons_before_amt,
    ship_qty,
    rev_qty,
    CAST( gs_decrypt(unit_cost_encrypt, '{secret_key}', 'AES128', 'CBC', 'SHA256') AS NUMERIC ) AS unit_cost,
    unit_price,
    rev_percent,
    carryover_rate,
    mgp_ratio,
    unit_cost_ytd_data,
    unit_price_ytd_data,
    rev_percent_ytd_data,
    carryover_rate_ytd_data,
    mgp_ratio_ytd_data,
    remark,
    created_by,
    creation_date,
    last_updated_by,
    last_update_date,
    del_flag
FROM
    dm_fop_dimension_tgt_period_filled_t
WHERE
    LEFT(target_period, 6) < {pred_version}
    AND del_flag = 'N'
    AND currency = 'CNY'
    AND bg_name IN ('运营商', '政企')
    and scenarios = '量纲子类'
    and dimension_subcategory_code <> 'NOSUB'
    AND version_code = (
        SELECT version_code
        FROM fin_dm_opt_fop.dm_fop_dimension_version_t
        WHERE step = 1
        AND LEFT(version_code, 6) = {pred_version}
        ORDER BY last_update_date DESC
        LIMIT 1
    )
    AND oversea_desc IN ('国内', '海外', '全球');