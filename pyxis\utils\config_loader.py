import os
from typing import Dict, Optional

from pyxis.decryption import EncryptType

from pyxis.utils.config_center.his_config_center_client import decrypt, HisConfigCenterClient, HisConfigCenterCredential
from pyxis.utils.config_template import <PERSON><PERSON>num, Config, InfraEnum
from pyxis.utils.logger.logger import get_base_logger
from pyxis.utils.obs.obs_client_pool import ObsClientPool
from pyxis.utils.rdb.sqlalchemy_pool import SQLAlchemyPool


class ConfigLoader:
    def __init__(self, config: dict):
        self.config = Config(**config)
        self.client = self.get_config_center_client()
        self.logger = self.get_logger()

    def get_config_center_client(self):
        if self.config.deploy.cloud == CloudEnum.HIS:
            credential = HisConfigCenterCredential(
                url=self.config.deploy.config_server.url,
                static_token=decrypt(
                    config_parts=self.config.deploy.config_server.config_parts,
                    work_key_cipher=os.environ.get("WORK_KEY_CIPHER", str()),
                    text=os.environ.get("STATIC_TOKEN", str()),
                    encrypt_type=EncryptType.ADV_2_6
                ),
                app_id=self.config.deploy.config_server.app_id,
                deployment_unit=self.config.deploy.config_server.du,
                region=self.config.deploy.config_server.region,
                environment=self.config.deploy.config_server.environment
            )
            return HisConfigCenterClient(self.config.deploy.api_gateway.authorization.url, credential)
        else:
            raise ValueError("Unknown Cloud Platform!")

    def get_rdb_pools(self) -> Dict[str, SQLAlchemyPool]:
        pools = dict()
        for db_name, db_config in self.config.rdb.items():
            pools[db_name] = SQLAlchemyPool(
                **self.client.get_rdb_config_by_name(datasource_name=db_config.datasource_name),
                **dict(db_config.model_dump(include={"tables", "echo"}))
            )

        return pools

    def get_obs_pools(self) -> Dict[str, ObsClientPool]:
        pools = dict()
        for bucket_name, bucket_config in self.config.obs.items():
            pools[bucket_name] = ObsClientPool(
                **{
                    **self.client.get_obs_config_by_bucket(bucket_config.bucket),
                    **dict(bucket_config.model_dump(exclude={"bucket"}))
                }
            )

        return pools

    def get_logger(self):
        if self.config.deploy.infra == InfraEnum.AIF:
            from aipaas.logger_factory import get_logger
            # In AIF environment, let AIF handle all logging to avoid duplication
            logger = get_logger("Logger", level=self.config.logger.level)
            # Disable propagation to prevent the message from being handled by root logger
            logger.propagate = False
            logger.warn("When using AIF logger, only level config will be set")
            return logger
        else:
            return get_base_logger(**self.config.logger.model_dump())


    def get_accounts_by_name(self, name: str) -> dict:
        try:
            result = self.client.get_account_by_name(name)
            if isinstance(result, dict):
                return result
            else:
                self.logger.warning(f"Unexpected return type from client: {type(result)}, value: {result}")
                return {"error": "unexpected response format from client", "raw": result}

        except Exception as e:
            self.logger.exception(f"Failed to get account by name '{name}': {e}")
            return {"error": "internal error", "message": str(e)}

    def get_application_config_by_key(self, key: str) -> Optional[str]:
        try:
            return self.client.get_application_config_by_key(key)
        except Exception as e:
            self.logger.exception(f"Failed to get config by key '{key}': {e}")
            return None
