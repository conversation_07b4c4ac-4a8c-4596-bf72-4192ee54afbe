from typing import List, Dict

import numpy as np
import pandas as pd

from src.expert_fusion_optimization.utils.default_conf import (
    NULL_STRING_PLACE_HOLDER,
    CHANYE_EXPERT_TYPE,
    CHANYE_EXPERT_MARK,
    DIM_EXPERT_TYPE,
    DIM_EXPERT_MARK
)
from src.expert_fusion_optimization.utils.func import get_other_columns
from src.time_series_pred.db.update_db import delete_date_by_expert_data_type, delete_date, update_table_with_replace
from src.time_series_pred.db import get_data
from src.utils.resource_loader import LOGGER as logger


def change_time(x):
    """修改时间列的格式函数

    Parameters
    ----------
    x： float 如202212

    Returns
    -------
    str 2022-12
    """
    a = str(x).split(".")[0]
    b = a[:4] + "-" + a[-2:]
    return b


def change(x):
    """修改存储到S3的分布数据的type列

    Parameters
    ----------
    x: str

    Returns
    -------
    str
    """
    if x == "mgp_rate":
        return "mgp_rate_after"
    elif x == "equip_rev_cons_after_amt":
        return "equip_rev_after"
    return x


def change_acc_method(data_concat):
    """修正acc列的属性

    Parameters
    ----------
    data_concat: 预测结果

    Returns
    -------
    dataframe
    """
    columns = ["unit_price_fcst_acc", "unit_cost_fcst_acc", "mgp_ratio_fcst_acc", "equip_rev_cons_after_amt_fcst_acc",
               "rev_percent_fcst_acc", "carryover_ratio_fcst_acc", "mca_adjust_ratio_acc", "mgp_adjust_ratio_acc"]
    for i in columns:
        if i in data_concat.columns:
            data_concat[i] = data_concat[i].apply(
                lambda x: np.nan if x == " " else x
            )
            data_concat[i] = data_concat[i].apply(lambda x: np.nan if x < -1e4 else x)
    return data_concat


def pg_write_data(data_concat: pd.DataFrame,
                  run_env: str,
                  wright_args: Dict,
                  delete_label: str = "all",
                  replace_blank_fields: List[str] = None
                  ):
    """预测结果写回数据库，需要先删除相同会计期数据，再写回

    Parameters
    ----------
    data_concat
        待写入数据
    run_env
        环境变量
    wright_args
        kv对参数
    delete_label
        删除表数据的策略标记
    replace_blank_fields
        写入数据表后，要将空格替换为空字符串的字段列表

    Returns
    -------
    存到数据库的数据
    """
    time = wright_args.get("time")
    table_name = wright_args.get("table_name")
    sql = f'SELECT * FROM {table_name} LIMIT 1'

    # 删除相同会计期的数据
    if wright_args.get("expert_data_type"):
        expert_data_type = wright_args.get("expert_data_type")
        expert_marks = {
            DIM_EXPERT_TYPE: DIM_EXPERT_MARK,
            CHANYE_EXPERT_TYPE: CHANYE_EXPERT_MARK
        }
        if expert_data_type != 'both' and expert_data_type not in expert_marks:
            raise ValueError(f"无效的专家数据类型: {expert_data_type}")
        if expert_data_type != 'both':
            delete_date_by_expert_data_type(table_name, time, expert_marks[expert_data_type])
        else:
            for k, v in expert_marks.items():
                delete_date_by_expert_data_type(table_name, time, v)
    else:
        delete_date(table_name, time)

    # 筛选字段
    data_result = get_data(sql)  # 获取要写入表的列名列表
    data_concat = get_other_columns(data_concat)  # 获取其他需要写入表的列名列表
    data_concat1 = data_concat[data_result.columns.tolist()]
    # 删除重复行
    data_concat1 = data_concat1.drop_duplicates()
    # 数据写入
    logger.info(f"待写入db的数据大小:{data_concat1.shape}")
    update_table_with_replace(data_concat1, table_name)
    logger.info("数据写入db操作完成")

    return data_concat1


def get_data_diff(data_res_ytd, data_res_ytd1, method_list):
    data_res_ytd["target_period"] = data_res_ytd["target_period"].apply(lambda x: str(x))
    data_res_ytd1["target_period"] = data_res_ytd1["target_period"].apply(lambda x: str(x))
    data_res_ytd1 = data_res_ytd1.sort_values(by=["unite", "target_period"]).reset_index(drop=True)
    data_res_ytd = data_res_ytd.sort_values(by=["unite", "target_period"]).reset_index(drop=True)
    for method in method_list:
        data_res_ytd1[method] = data_res_ytd1[method + "_fcst"] - data_res_ytd[method + "_fcst"]
        data_res_ytd1[method + "_fcst_lower"] = data_res_ytd1[method + "_fcst_lower"] + data_res_ytd1[method]
        data_res_ytd1[method + "_fcst_upper"] = data_res_ytd1[method + "_fcst_upper"] + data_res_ytd1[method]
    return data_res_ytd1


def make_unite(train_data):
    if "l2_name" in train_data.columns.tolist():
        unite_cols = ["bg_name", "lv1_name", "lv2_name", "l1_name", "l2_name"]
        train_data["unite"] = train_data[unite_cols].apply("_".join, axis=1)
    elif "l1_name" in train_data.columns.tolist():
        unite_cols = ["bg_name", "lv1_name", "lv2_name", "l1_name"]
        train_data["unite"] = train_data[unite_cols].apply("_".join, axis=1)
    else:
        unite_cols = ["bg_name", "lv1_name"]
        train_data["unite"] = train_data[unite_cols].apply("_".join, axis=1)

    return train_data



def get_last_year_period_id(period_id: str) -> str:
    """获取去年同期的会计期
    """
    res = str(int(period_id[:4]) - 1) + period_id[4:]

    return res


def get_last_month_period_id(period_id: str) -> str:
    """获取当前会计期上个月的会计期
    """
    year = int(period_id[:4])
    month = int(period_id[4:])
    if month > 1:
        last_period_id = str(year) + "%02d" % (month - 1)
    else:
        last_period_id = str(year - 1) + "12"

    return last_period_id


def get_last_quarter_3period_id(period_id: str) -> List[str]:
    """获取上一个季度的三个会计期
    """
    month = int(period_id[4:])
    quarter = (month - 1) // 3 + 1
    last_quarter_year = int(period_id[:4]) if quarter > 1 else int(period_id[:4]) - 1
    res = []
    for m in range(3):
        last_quarter = quarter - 1 if quarter > 1 else 4
        last_quarter_month = m + 1 + 3 * (last_quarter - 1)
        res.append(str(last_quarter_year) + "%02d" % last_quarter_month)

    return res


def get_last_year_special_month_period_id(period_id: str,
                                          month_list: List[int]
                                          ) -> List[str]:
    """获取上一年度指定月份的会计期
    """
    last_year = int(period_id[:4]) - 1

    return [str(last_year) + "%02d" % month for month in month_list]


def add_target_period(period_id,
                      target_code):
    year = period_id[:4]
    month = int(period_id[4:])

    if target_code == 'Y':
        return year
    elif target_code == 'Q':
        quarter = (month - 1) // 3 + 1
        return f"{year}Q{quarter}"
    elif target_code == 'H':
        half = 1 if month <= 6 else 2
        return f"{year}H{half}"
    return None


def get_target_period_level(target_period: str) -> str:
    """获取目标预测期次的时间粒度
    """
    target_period_level = NULL_STRING_PLACE_HOLDER
    if len(target_period) == 4 and target_period.isdigit():
        target_period_level = "year"
    elif len(target_period) == 6 and target_period[4].lower() == "h":
        target_period_level = "half-a-year"
    elif len(target_period) == 6 and target_period[4].lower() == "q":
        target_period_level = "quarter"
    elif len(target_period) == 6 and target_period.isdigit():
        target_period_level = "month"

    return target_period_level
