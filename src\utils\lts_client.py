import argparse
import os

import requests

from pyxis.utils.authorization.his_authorization import get_dynamic_token_without_param
from src.utils.resource_loader import LOGGER as logger


def lts_back(lts_job_id, status):
    logger.info(f"LTS回调接口开始-status:{status} ---")
    # LTS回调接口url、生成人、APPID、token定义
    if os.environ.get("ENV", "LOCAL") == "PROD-AIF":
        lts_url = "https://lts.fin.his.huawei.com/lts/service/v1/job/commitAsyncJobStatus"
    else:
        lts_url = "https://lts.biz.his-beta.huawei.com/lts/service/v1/job/commitAsyncJobStatus"

    # 生成动态Token
    authorization = get_dynamic_token_without_param()
    # 回调接口header定义
    header = {'Authorization': str(authorization)}
    # 回调接口body定义
    data = {
        "asyncjobinfo": "{'LTS_JOB_ID':'%s','LTS_JOB_STATUS':'%s','V': '1.0'}" % (lts_job_id, status),
        "w3c": 'zhouboxiao',
        "source": "lts",
        "appid": 'com.huawei.finance.ai.opt.fop'
    }
    # 接口运行
    response = requests.post(lts_url, headers=header, data=data, verify=False)
    logger.info(f"LTS回调接口结束-status:{status}---")
    return response.text


def parse_args():
    """
    解析LTS输入参数
    :return:
    """
    parser = argparse.ArgumentParser(description="aipaas demo args")
    parser.add_argument("--EXEC_DATE", help="help of EXEC_DATE")
    parser.add_argument("--LTS_JOB_ID", help="help of LTS_JOB_ID")
    parser.add_argument("--TARGET_LEVEL", help="help of TARGET_LEVEL")
    parser.add_argument("--METHOD", help="help of METHOD")
    args = parser.parse_args()
    # 日志打印
    logger.info("EXEC_DATE:{}".format(args.EXEC_DATE) + "---")
    logger.info("LTS_JOB_ID:{}".format(args.LTS_JOB_ID) + "---")
    logger.info("TARGET_LEVEL:{}".format(args.TARGET_LEVEL) + "---")
    logger.info("METHOD:{}".format(args.METHOD) + "---")
    return args


def parse_args_for_combine():
    """
    解析AI预测融合专家预测结果任务LTS输入参数
    """
    parser = argparse.ArgumentParser(description="aipaas demo args")
    parser.add_argument("--EXEC_DATE", help="help of EXEC_DATE")
    parser.add_argument("--LTS_JOB_ID", help="help of LTS_JOB_ID")
    parser.add_argument("--EXPERT_TYPE", help="group,chanye,both")
    args = parser.parse_args()
    # 日志打印
    logger.info("EXEC_DATE:{}".format(args.EXEC_DATE) + "---")
    logger.info("LTS_JOB_ID:{}".format(args.LTS_JOB_ID) + "---")
    logger.info("EXPERT_TYPE:{}".format(args.EXPERT_TYPE) + "---")

    return args

