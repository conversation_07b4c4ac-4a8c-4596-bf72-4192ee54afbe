#!/usr/bin/env python3
"""
测试业务服务重启处理逻辑

这个脚本用于测试改进后的API错误处理和轮询逻辑。
"""

import json
import time
from unittest.mock import Mock, patch
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_error_detection():
    """测试错误检测逻辑"""
    print("=== 测试错误检测逻辑 ===")
    
    # 模拟不同类型的API响应
    test_cases = [
        {
            "name": "业务服务重启错误",
            "response": {
                "httpCode": 500,
                "errorCode": "1405",
                "message": "业务服务挂掉重启"
            },
            "expected": "应该被识别为服务重启错误"
        },
        {
            "name": "普通业务错误",
            "response": {
                "httpCode": 400,
                "errorCode": "1001",
                "message": "参数错误"
            },
            "expected": "应该被识别为普通错误"
        },
        {
            "name": "成功响应",
            "response": {
                "httpCode": 200,
                "data": "http://example.com/result.csv"
            },
            "expected": "应该返回数据"
        }
    ]
    
    for case in test_cases:
        print(f"\n测试用例: {case['name']}")
        print(f"响应: {json.dumps(case['response'], ensure_ascii=False)}")
        print(f"预期: {case['expected']}")
        
        # 检测逻辑
        response = case['response']
        error_code = response.get("errorCode", "")
        error_message = response.get("message", "")
        
        is_service_restart = ("1405" in str(error_code) or 
                             "业务服务" in error_message or 
                             "重启" in error_message)
        
        if is_service_restart:
            print("✓ 检测结果: 服务重启错误")
        elif response.get("httpCode") == 200 and response.get("data"):
            print("✓ 检测结果: 成功响应")
        else:
            print("✓ 检测结果: 普通错误")

def test_consecutive_failure_logic():
    """测试连续失败检测逻辑"""
    print("\n\n=== 测试连续失败检测逻辑 ===")
    
    consecutive_failures = 0
    max_consecutive_failures = 5
    
    # 模拟轮询过程
    poll_results = [False, False, False, False, False, True, False, True]
    
    for i, success in enumerate(poll_results):
        print(f"\n第{i+1}次轮询:")
        
        if success:
            print("  轮询成功")
            consecutive_failures = 0
        else:
            consecutive_failures += 1
            print(f"  轮询失败，连续失败次数: {consecutive_failures}")
            
            if consecutive_failures >= max_consecutive_failures:
                print("  ⚠️  检测到可能的服务重启")
                consecutive_failures = 0  # 重置计数器

def test_token_refresh_logic():
    """测试Token刷新逻辑"""
    print("\n\n=== 测试Token刷新逻辑 ===")
    
    token_refresh_interval = 300  # 5分钟
    start_time = time.time()
    
    # 模拟不同时间点的检查
    time_points = [0, 150, 300, 450, 600]  # 0, 2.5分钟, 5分钟, 7.5分钟, 10分钟
    
    for elapsed in time_points:
        current_time = start_time + elapsed
        should_refresh = (current_time - start_time) > token_refresh_interval
        
        print(f"\n经过时间: {elapsed}秒 ({elapsed/60:.1f}分钟)")
        print(f"是否需要刷新Token: {'是' if should_refresh else '否'}")
        
        if should_refresh:
            print("  🔄 执行Token刷新")
            start_time = current_time  # 更新刷新时间

def test_wait_time_calculation():
    """测试等待时间计算逻辑"""
    print("\n\n=== 测试等待时间计算逻辑 ===")
    
    service_restart_wait_multiplier = 2
    max_service_restart_wait = 30
    base_interval = 10
    
    scenarios = [
        {"service_restart": False, "description": "正常轮询"},
        {"service_restart": True, "description": "检测到服务重启"}
    ]
    
    for scenario in scenarios:
        print(f"\n场景: {scenario['description']}")
        
        if scenario['service_restart']:
            wait_time = min(base_interval * service_restart_wait_multiplier, max_service_restart_wait)
            print(f"  等待时间: {wait_time}秒 (延长等待)")
        else:
            wait_time = base_interval
            print(f"  等待时间: {wait_time}秒 (正常间隔)")

def test_configuration_loading():
    """测试配置加载"""
    print("\n\n=== 测试配置加载 ===")
    
    # 模拟配置
    mock_config = {
        "max_consecutive_failures": 5,
        "token_refresh_interval": 300,
        "service_restart_wait_multiplier": 2,
        "max_service_restart_wait": 30
    }
    
    print("配置参数:")
    for key, value in mock_config.items():
        print(f"  {key}: {value}")
    
    # 测试默认值
    print("\n使用getattr获取配置 (带默认值):")
    class MockConfig:
        def __init__(self, config_dict):
            for key, value in config_dict.items():
                setattr(self, key, value)
    
    config = MockConfig(mock_config)
    
    test_params = [
        ("max_consecutive_failures", 3),
        ("token_refresh_interval", 600),
        ("non_existent_param", 999)
    ]
    
    for param, default in test_params:
        value = getattr(config, param, default)
        print(f"  {param}: {value} {'(默认值)' if not hasattr(config, param) else '(配置值)'}")

if __name__ == "__main__":
    print("业务服务重启处理逻辑测试")
    print("=" * 50)
    
    test_error_detection()
    test_consecutive_failure_logic()
    test_token_refresh_logic()
    test_wait_time_calculation()
    test_configuration_loading()
    
    print("\n\n" + "=" * 50)
    print("测试完成！")
    print("\n建议:")
    print("1. 在实际环境中监控日志输出")
    print("2. 根据实际情况调整配置参数")
    print("3. 定期检查API服务状态")
    print("4. 设置适当的告警机制")
