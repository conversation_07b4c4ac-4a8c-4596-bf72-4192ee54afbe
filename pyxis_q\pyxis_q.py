import functools
import itertools
import json
import logging
import sys
import time
import traceback
import concurrent.futures
from dataclasses import asdict, dataclass, fields
from datetime import datetime, timezone, timedelta
from enum import Enum
from multiprocessing import Process, Queue
from typing import List, Set

import psycopg2
import psycopg2.extras
from psycopg2.extensions import ISOLATION_LEVEL_READ_COMMITTED

from pyxis_q import constant
from pyxis_q.model import TaskDbModel
from pyxis_q.registry import TASK_REGISTRY
from pyxis_q.scripts import FETCH_TASK, UPDATE_TASK


class TaskStrategy(str, Enum):
    FIFO = "fifo"
    PRIORITY_FIFO = "priority_fifo"


FETCH_STRATEGY = {
    TaskStrategy.FIFO.value: "creation_date ASC",
    TaskStrategy.PRIORITY_FIFO.value: "priority DESC, creation_date ASC",
}


@dataclass(frozen=True)
class PostgresConfig:
    password: str
    db_name: str = "postgres"
    user: str = "postgres"
    host: str = "localhost"
    port: int = 5432
    schema: str = "pyxis_q"
    table: str = "task_q"


def time_limiter(seconds: float):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(func, *args, **kwargs)
                result = future.result(timeout=seconds)
            return result

        return wrapper

    return decorator


def _task_rollback(task: TaskDbModel):
    if task.execution_count <= task.max_retries:
        task.task_status = constant.TaskStatus.PENDING.value
        task.ignore_before = datetime.now(tz=timezone.utc) + timedelta(seconds=task.retry_interval_in_seconds)
    else:
        task.task_status = constant.TaskStatus.FAILED.value


def _format_task_for_saving(task: TaskDbModel):
    task.args = json.dumps(obj=task.args, ensure_ascii=False)
    task.last_update_by = datetime.now(tz=timezone.utc)
    task.task_result = json.dumps(obj=task.task_result, ensure_ascii=False) if task.task_result else None
    task.error = json.dumps(obj=task.error, ensure_ascii=False)


def _task_runner(task_queue: Queue, result_queue: Queue, max_task_per_child: int):
    for _ in range(max_task_per_child):
        task = task_queue.get()
        start_perf = time.perf_counter()
        task.execution_count += 1
        try:
            result = time_limiter(task.time_limit)(TASK_REGISTRY[task.task_name])(**task.args)
            task.task_result = result
            task.error[task.execution_count] = None
            task.task_status = constant.TaskStatus.SUCCESS.value
        except concurrent.futures.TimeoutError:
            task.error[task.execution_count] = "time limit exceeded"
            task.task_result = None
            _task_rollback(task)
        except Exception:
            task.error[task.execution_count] = traceback.format_exc()
            task.task_result = None
            _task_rollback(task)

        end_perf = time.perf_counter()
        elapsed_sec = end_perf - start_perf
        task.elapsed_sec = round(elapsed_sec, 5)

        _format_task_for_saving(task)
        result_queue.put(task)


class PyxisQ(object):
    def __init__(
        self,
        password: str,
        database: str = "postgres",
        user: str = "postgres",
        host: str = "localhost",
        port: int = 5432,
        schema: str = "pyxis_q",
        table: str = "task_q",
        *,
        num_workers: int = 1,
        task_group: Set[str] = frozenset({"default"}),
        fetch_strategy: str = TaskStrategy.PRIORITY_FIFO,
        batch_size: int = 1,
        max_task_per_child: int = 40,
        polling_interval: float = 1,
        logger=None,
        conn_recycle_interval_sec=10 * 60,
        **kwargs
    ):
        self._password = password
        self._db_name = database
        self._user = user
        self._host = host
        self._port = port
        self._schema = schema
        self._table = table

        self._num_workers = num_workers
        self._cyclic_group = itertools.cycle(list(task_group))
        self._fetch_strategy = fetch_strategy
        self._batch_size = batch_size
        self._max_task_per_child = max_task_per_child
        self._polling_interval = polling_interval
        self._pool = None
        self._task_queue = Queue()
        self._result_queue = Queue()
        self._conn = None
        self._logger = logger
        self._start = None
        self._count = 0
        self._conn_create_time = None
        self._conn_recycle_interval_sec = conn_recycle_interval_sec

    def _init_logger(self):
        if self._logger is None:
            self._logger = logging.getLogger("pyxis_q")
            self._logger.setLevel(logging.INFO)
            stream_handler = logging.StreamHandler(sys.stdout)
            stream_handler.setFormatter(logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s"))
            self._logger.addHandler(stream_handler)

    def _init_broker(self):
        while True:
            try:
                self._conn = psycopg2.connect(
                    dbname=self._db_name,
                    user=self._user,
                    password=self._password,
                    host=self._host,
                    port=self._port,
                    options=f"-c search_path={self._schema}"
                )
                break
            except Exception as e:
                self._logger.warning(f"Database connection failed: {str(e)}")

        self._conn.set_isolation_level(ISOLATION_LEVEL_READ_COMMITTED)
        self._conn_create_time = datetime.now(tz=timezone.utc)

    def _launch_worker(self):
        worker = Process(target=_task_runner, args=(self._task_queue, self._result_queue, self._max_task_per_child))
        worker.start()
        return worker

    def _init_pool(self):
        self._pool = [self._launch_worker() for _ in range(self._num_workers)]
        self._logger.info(f"Launched worker: {self._num_workers}")

    def _fetch_tasks_from_broker(self, task_group: str, batch_size: int) -> List[TaskDbModel]:
        cols = tuple(key.name for key in fields(TaskDbModel))
        try:
            with self._conn:
                with self._conn.cursor() as cur:
                    cur.execute(
                        FETCH_TASK.format(
                            schema=self._schema,
                            table=self._table,
                            cols=", ".join(cols),
                            strategy=FETCH_STRATEGY[self._fetch_strategy],
                            pre_fetch=batch_size
                        ),
                        {"task_group": task_group, "task_names": tuple(TASK_REGISTRY.keys())}
                    )
                    tasks = [TaskDbModel(**dict(zip(cols, task))) for task in cur.fetchall()]

        except Exception as e:
            self._logger.warning(f"Database task fetch query failed: {str(e)}")
            tasks = []
            self._conn.close()
            self._init_broker()

        if tasks:
            self._logger.info(f"Batch of {len(tasks)} tasks received from {task_group}: {tasks}")
        return tasks

    def _filter_task(self, tasks) -> list:
        for task in tasks:
            if task.task_name not in TASK_REGISTRY.keys():
                task.error[task.execution_count] = "task name not registered"
                task.task_result = None
                task.execution_count += 1
                _task_rollback(task)
                _format_task_for_saving(task)
                tasks.remove(task)
                self._result_queue.put(task)
        return tasks

    def _submit_tasks(self, tasks: List[TaskDbModel]) -> None:
        for task in tasks:
            self._task_queue.put(task)

    def _get_task_results(self) -> List[TaskDbModel]:
        completed_tasks = []
        while not self._result_queue.empty():
            completed_tasks.append(self._result_queue.get())
        return completed_tasks

    def _restore_tasks_to_queue(self, tasks: List[TaskDbModel]) -> None:
        for task in tasks:
            self._result_queue.put(task)

    def _save_tasks_to_broker(self, tasks: List[TaskDbModel]):
        try:
            if tasks:
                with self._conn:
                    with self._conn.cursor() as cur:
                        psycopg2.extras.execute_batch(
                            cur=cur,
                            sql=UPDATE_TASK.format(schema=self._schema, table=self._table),
                            argslist=[asdict(task) for task in tasks]
                        )
                self._logger.info(f"Batch of {len(tasks)} tasks saved: {tasks}")
        except Exception as e:
            self._logger.warning(f"Database task save failed: {str(e)}")
            self._restore_tasks_to_queue(tasks)
            self._conn.close()
            self._init_broker()

    def _report_statistics(self, completed_tasks: List[TaskDbModel]):
        self._count += len(completed_tasks)
        if completed_tasks:
            self._logger.info("--- {:02f} seconds elapsed ---".format(time.time() - self._start))
            self._logger.info(f"Num of tasks executed: {self._count}")

    def _sentinel(self):
        for i, worker in enumerate(self._pool):
            if not self._pool[i].is_alive():
                self._pool[i] = self._launch_worker()
                self._logger.info(f"Restart No.{i} worker, new pid: {worker.pid}")

        if datetime.now(tz=timezone.utc) - self._conn_create_time > timedelta(seconds=self._conn_recycle_interval_sec):
            self._init_broker()

    def run(self):
        self._start = time.time()
        self._init_logger()
        self._init_broker()
        self._init_pool()

        while True:
            # 拉取任务
            if self._task_queue.empty():
                tasks = self._fetch_tasks_from_broker(task_group=next(self._cyclic_group), batch_size=self._batch_size)
                self._submit_tasks(self._filter_task(tasks))

            # 保存结果
            completed_tasks = self._get_task_results()
            self._save_tasks_to_broker(completed_tasks)

            # 心跳监控
            self._report_statistics(completed_tasks)
            self._sentinel()
            time.sleep(self._polling_interval)

    def __del__(self):
        """
        释放资源
        """
        if self._conn is not None:
            self._conn.close()
        if self._pool is not None:
            for i in range(self._num_workers):
                self._pool[i].join()
