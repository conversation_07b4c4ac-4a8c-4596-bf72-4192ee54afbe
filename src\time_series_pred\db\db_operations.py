from functools import wraps

from src.utils.resource_loader import RDB_POOL, LOGGER as log


def with_db_session(commit: bool = False, pool=None):
    """
    数据库会话装饰器

    Args:
        commit: 是否自动提交事务
        pool: 数据库连接池，默认使用全局RDB_POOL

    Usage:
        @with_db_session(commit=True)
        def my_db_operation(session, param1, param2):
            # 数据库操作
            session.execute(...)
            return result
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            db_pool = pool or RDB_POOL
            with db_pool.context_session(commit=commit) as session:
                try:
                    return func(session, *args, **kwargs)
                except Exception as e:
                    session.rollback()
                    log.error(f"数据库操作失败 in {func.__name__}: {str(e)}")
                    raise
        return wrapper
    return decorator