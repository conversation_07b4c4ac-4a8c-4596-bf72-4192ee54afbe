from typing import Dict, <PERSON><PERSON>

import numpy as np
import pandas as pd

from AutoTsPred.experiment.cad import ConditionalAnomalyDetection, ConditionalReferenceDetection
from AutoTsPred.experiment.ccpd import ConditionalCPD
from AutoTsPred.experiment.clt import ConditionalLineTrend
from AutoTsPred.experiment.coutput import ConditionalOutput
from AutoTsPred.experiment.cpred import ConditionalBottomPredict
from AutoTsPred.experiment.ts_data import TimeSeriesData
from AutoTsPred.pipeline import Pipeline
from AutoTsPred.utils.default_conf import FILL_DATA_TAG
from AutoTsPred.utils.exceptions import ParamError
from AutoTsPred.utils.validate_param import check_param_type

from src.utils.resource_loader import LOGGER as log


def prepare_data(df: pd.DataFrame):
    # 准备数据
    ts_data = TimeSeriesData(data=df)

    # 集团盈利预测数据约定传入的列
    ts_data.name = ts_data.data["UNION_ATTR"].iloc[0]  # 时序列名

    fill_data_tag = FILL_DATA_TAG  # 规则预测填充的值
    if fill_data_tag in list(df.columns):
        try:
            fill_data = float(df[fill_data_tag].iloc[0])
            check_param_type("fill_data", fill_data, [float], allow_null=False)
            ts_data.tags[fill_data_tag] = fill_data
        except ParamError as e:
            msg = f"获取规则预测填充值失败，请检查。异常信息:{e}"
            log.error(msg)

    data_missing_tag = "Miss"  # 高缺失率标记列
    if data_missing_tag in list(df.columns) and str(df[data_missing_tag].iloc[0]).lower() == "true":
        ts_data.tags[data_missing_tag] = True
    return ts_data, data_missing_tag


def check_pipeline(df: pd.DataFrame,
                   params: Dict
                   ) -> Tuple[pd.DataFrame, Dict, np.ndarray, Dict, str]:
    '''
    三个异常检测pipline调用
    :param df:
    :param params:
    :return:
    '''

    ts_data, data_missing_tag = prepare_data(df)

    # 组织pipeline参数
    pred_result_tag = "predict_result"

    # 编排pipeline的组件steps
    steps = [
        # 异常检测
        (ConditionalAnomalyDetection, {
            "condition_params": {
                "judge_reject_tags": [data_missing_tag],
                "result_tag": "anomaly_index",
                "anomaly_entry_conditions": params.get("anomaly_entry_conditions"),
                "anomaly_model_name": params.get("anomaly_model_name"),
                "anomaly_kwargs": params.get("anomaly_kwargs"),
            }
        }),
        # 变点检测
        (ConditionalCPD, {
            "condition_params": {
                "judge_reject_tags": [data_missing_tag],
                "result_tag": "cpd_index",
                "thres": params.get("bayes_p_value"),  # 贝叶斯变点检测p值
                "is_truncated": params.get("is_truncated"),  # 是否按变点截断数据
                "ignore_last_n": params.get("ignore_last_n"),  # 截断数据时忽略最后n个时点中识别出的变点
            }
        }),
        # 输出结果
        (ConditionalOutput, {
            "condition_params": {
                "result_tag": pred_result_tag,
                "adjust_tag": params.get("adjust_tag", False),  # 调整预测结果：保持周期内递增
            }
        }),

    ]

    p = Pipeline(steps)
    tuple_res = p.transform(y=ts_data)  # fh此处不起作用
    return tuple_res


def reference_pipline_trend(df: pd.DataFrame,
                            params: Dict
                            ) -> Tuple[pd.DataFrame, Dict, np.ndarray, Dict, str]:
    '''
    数据可参考性判断
    :param df:
    :param params:
    :return:
    '''
    ts_data, data_missing_tag = prepare_data(df)

    # 组织pipeline参数
    reference_tag = "reference_detection_index"
    pred_result_tag = "predict_result"

    steps = [
        (ConditionalReferenceDetection, {
            "condition_params": {
                "judge_reject_tags": [data_missing_tag],
                "result_tag": reference_tag,
                "reference_detection": params.get("reference_detection"),
                "reference_params": params.get("reference_params", {"fill_name": "fill_data"})
            }
        }),
        # 输出结果
        (ConditionalOutput, {
            "condition_params": {
                "result_tag": pred_result_tag,
                "adjust_tag": params.get("adjust_tag", False),  # 调整预测结果：保持周期内递增
            }
        }),
    ]

    p = Pipeline(steps)
    tuple_res = p.transform(y=ts_data)  # fh此处不起作用

    if reference_tag in tuple_res.tags:
        return True
    else:
        return False


def transform_conditionalLineTrend(df: pd.DataFrame,
                                   params: Dict
                                   ) -> Tuple[pd.DataFrame, Dict, np.ndarray, Dict, str]:
    '''
    是否有线性外推趋势判断
    :param df:
    :param params:
    :return:
    '''
    # 准备数据
    ts_data, data_missing_tag = prepare_data(df)

    # 组织pipeline参数
    reference_tag = "reference_detection_index"
    pred_result_tag = "predict_result"

    steps = [
        # 线性外推判断
        (ConditionalLineTrend, {
            "condition_params": {
                "judge_reject_tags": [data_missing_tag, reference_tag],
                "result_tag": "line_trend",
                "line_trend_thres": params.get("line_trend_thres", 0.7),  # 同正同负比例
            }
        }),
        # 输出结果
        (ConditionalOutput, {
            "condition_params": {
                "result_tag": pred_result_tag,
                "adjust_tag": params.get("adjust_tag", False),  # 调整预测结果：保持周期内递增
            }
        }),
    ]

    p = Pipeline(steps)
    tuple_res = p.transform(y=ts_data)  # fh此处不起作用

    params["ts_name"] = ts_data.name
    if "line_trend" in tuple_res.tags:  # 如果有线性外推趋势，则返回True
        return True
    else:
        return False


def predict_exception_handler(ts_data: TimeSeriesData,
                              params: Dict):
    """时序预测任务呢异常的处理handler
    """
    pred_result_tag = params.get("result_tag")
    bottom = ConditionalBottomPredict(condition_params={
        "confidence": params.get("confidence"),
        "steps": params.get("steps"),
        "result_tag": pred_result_tag,
    })
    bottom.transform(ts_data)
    res = bottom.ts_data

    return res
