# opt.fcst.profits.model

# dev-start

## environment variables

```
AIPAAS_CONSOLE_SWITCH  =  1
OTHER_PARAM_SOLUTIONID  =  11310
UNIT_PRICE_SOLUTIONID  =  11308
UNIT_COST_SOLUTIONID  =  11306
TASK_ID  =  585942
PROBLEM_ID  =  585942
PROCESS_NUM  =  1
SAVE_DB_ENABLED  =  false
APPID  =  com.huawei.finance.ai.opt.fop
WORK_KEY_CIPHER  =  {从配置中心获取}
STATIC_TOKEN  =  {从配置中心获取}
ENV  =  SIT-AIF
MAIN_KEY  =  {从配置中心获取}
J2C_SALT_ONE  =  2,3,69,85,100,21,33,76,12,17,63,25,53,69,8,69
ASSIST_KEY  =  {从配置中心获取}
```
- MAIN_KEY 主根秘钥：用于静态token解密和J2C解密
- ASSIST_KEY 辅根秘钥：用于静态token解密和J2C解密
- J2C_SALT_ONE 盐：用于静态token解密和J2C解密
- STATIC_TOKEN 加密后的静态token
- WORK_KEY_CIPHER 解密组件：用于静态token解密
- SAVE_DB_ENABLED  =  false  本地需要关闭写入数据库


## shell
```shell

uvicorn main:app --host 0.0.0.0 --port 8088 --workers 1 &
```


# 本地测试


```
curl --location 'http://localhost:8088/v1/prediction-tasks/start' \
--header 'Accept: application/json, text/plain, */*' \
--header 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
--header 'Connection: keep-alive' \
--header 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
--header 'x-traceid: 04e000f3-6f60-4dbd-8371-a9c2f535a98c' \
--header 'x-appid: com.huawei.finance.ai.opt.fop' \
--header 'x-userid: -1' \
--header 'Authorization: Basic XXXX' \
--header 'x-tenantid: -1' \
--header 'Content-Type: application/json' \
--data '{
    "task_name":"时序预测",
    "params":  {
        "pred_version": 202510
    },
    "env": "sit"
}'
```
- pred_version 需要修改成自定义的会计期
- Authorization 需要填写正确的动态token


# Run test

手动执行`run_tests.py`, 可执行所有的UT

执行结果样例：
```shell

开始运行pytest测试...
STDOUT:
============================= test session starts =============================
platform win32 -- Python 3.9.13, pytest-7.4.4, pluggy-1.6.0 -- D:\code-hub\opt.fcst.profits.model\venv\Scripts\python.exe
cachedir: .pytest_cache
rootdir: D:\code-hub\opt.fcst.profits.model\opt.fcst.profits.model
configfile: pytest.ini
plugins: anyio-4.9.0
collecting ... collected 89 items

tests/test_combine_data.py::test_read_to_combine_data_from_db SKIPPED    [  1%]
...
...
...
tests/test_times_data_process.py::test_aggregate_forecast_data_normal_case PASSED [100%]

======================== 83 passed, 6 skipped in 8.37s ========================

✅ 所有测试通过!

Process finished with exit code 0

```


