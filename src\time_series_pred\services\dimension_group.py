from pathlib import Path

from src.time_series_pred.common.dimension_constant import dimension_group_dim, dimension_fcst_group_by_cols
from src.time_series_pred.db.get_data import get_data
from src.time_series_pred.db.save_to_db import save_to_db
from src.time_series_pred.services.process_after_pred import correct_cost_price_anomalies
from src.time_series_pred.services.process_after_pred import post_process_non_nagtive
from src.time_series_pred.services.profits_sequence_pred import integrate_results
from src.utils.util import check_null_values, aggregate_pred_data, save_to_local

# 预测因子
dimension_pred_lst = ['unit_cost', 'unit_price', 'carryover_rate']
nondimension_pred_lst = ['rev_percent', 'mgp_ratio']
# 区间预测
dim_need_interval_prediction = ['unit_cost', 'unit_price']
cols_to_check = dimension_pred_lst+nondimension_pred_lst


def profit_forecast(pred_version: int):
    try:
        sql_path = Path(__file__).parent.parent / "sql" / "dimension_group.sql"
        with open(sql_path, "r", encoding="utf-8") as f:
            sql_template = f.read()
        sql_query = sql_template.format(pred_version=pred_version)
        df = get_data(sql_query)
    except Exception as e:
        raise RuntimeError(f"Database query failed: {str(e)}") from e

    # 检查结果
    check_null_values(df, cols_to_check)
    # 有量纲分组
    dimension_group_his_df = df[(df['scenarios'] == '量纲分组') & (df['dimension_group_code'] != 'NODIM')]

    dimension_group_pred_df = integrate_results(his_df=dimension_group_his_df, dim=dimension_group_dim,
                                                pred_cols=dimension_pred_lst, period_col='target_period',
                                                pred_version=pred_version)

    valid_cols = [col for col in dimension_fcst_group_by_cols if col in dimension_group_pred_df.columns]
    df = aggregate_pred_data(
        dimension_group_pred_df,
        valid_cols,
        dimension_pred_lst,
        dim_need_interval_prediction
    )

    df['period_id'] = pred_version
    # 异常后处理
    correct_cost_price_anomalies(df, dimension_group_his_df, dimension_group_dim)
    # 对均本均价的非负化处理
    df = post_process_non_nagtive(df=df, his_df=dimension_group_his_df, dim=dimension_group_dim,
                                  data_cols=dimension_pred_lst)

    save_to_local(df, "dimension_group_pred")

    save_to_db(df, scenarios='DIM')
