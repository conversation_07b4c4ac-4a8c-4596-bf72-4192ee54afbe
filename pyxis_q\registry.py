TASK_REGISTRY = dict()


def add_task(func, name: str):
    """
    Register a function as a task
    """
    task_name = name if name else func.__name__
    TASK_REGISTRY[task_name] = func
    return func


def register(name: str = None):
    """
    Register a function as a task
    """

    def decorator(func):
        task_name = name if name else func.__name__
        TASK_REGISTRY[task_name] = func
        return func

    return decorator
