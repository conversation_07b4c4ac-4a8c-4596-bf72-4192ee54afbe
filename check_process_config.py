#!/usr/bin/env python3
"""
进程配置检查脚本
用于检查当前环境的进程配置是否合理，避免进程爆炸问题
"""

import os
import multiprocessing
import subprocess
import sys


def get_current_python_processes():
    """获取当前运行的Python进程数"""
    try:
        if sys.platform == "win32":
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            lines = result.stdout.strip().split('\n')
            # 过滤掉标题行
            python_processes = [line for line in lines if 'python.exe' in line.lower()]
            return len(python_processes)
        else:
            result = subprocess.run(['pgrep', '-f', 'python'], capture_output=True, text=True)
            return len(result.stdout.strip().split('\n')) if result.stdout.strip() else 0
    except Exception as e:
        print(f"无法获取进程信息: {e}")
        return 0


def check_process_configuration():
    """检查进程配置"""
    print("=" * 60)
    print("进程配置检查报告")
    print("=" * 60)
    
    # 系统信息
    cpu_count = multiprocessing.cpu_count()
    print(f"CPU核心数: {cpu_count}")
    
    # 环境变量
    process_num = int(os.environ.get('PROCESS_NUM',1))
    worker_num = int(os.environ.get('WORKER_NUM', 1))
    concurrent_tasks = int(os.environ.get('CONCURRENT_TASKS', 7))  # 7个预测场景

    print(f"PROCESS_NUM (每个任务的进程数): {process_num}")
    print(f"WORKER_NUM (uvicorn worker数): {worker_num}")
    print(f"CONCURRENT_TASKS (并发任务数): {concurrent_tasks}")

    # 计算理论进程数（考虑三层并发）
    theoretical_max = worker_num * concurrent_tasks * process_num + worker_num + 1  # +1 for master process
    recommended_max = cpu_count * 2

    print(f"\n理论最大进程数: {theoretical_max}")
    print(f"  - 1个主进程 (uvicorn master)")
    print(f"  - {worker_num}个worker进程 (uvicorn workers)")
    print(f"  - {worker_num} × {concurrent_tasks} × {process_num} = {worker_num * concurrent_tasks * process_num}个子进程")
    print(f"    (每个worker运行{concurrent_tasks}个并发任务，每个任务创建{process_num}个进程)")
    
    print(f"推荐最大进程数: {recommended_max} (CPU核心数 × 2)")
    
    # 当前运行的Python进程
    current_processes = get_current_python_processes()
    print(f"当前Python进程数: {current_processes}")
    
    # 风险评估
    print("\n" + "=" * 60)
    print("风险评估")
    print("=" * 60)
    
    if theoretical_max > recommended_max:
        print("⚠️  警告: 当前配置可能导致进程爆炸!")
        print(f"   理论进程数 ({theoretical_max}) > 推荐最大值 ({recommended_max})")
        
        # 建议的配置
        if worker_num > 1:
            suggested_process_num = max(1, (recommended_max - worker_num - 1) // (worker_num * concurrent_tasks))
            print(f"\n建议调整:")
            print(f"   方案1: 保持WORKER_NUM={worker_num}, 调整PROCESS_NUM={suggested_process_num}")
            print(f"   方案2: 设置WORKER_NUM=1, 调整PROCESS_NUM={max(1, recommended_max // concurrent_tasks)}")
            print(f"   方案3: 减少并发任务数，使用顺序执行")
        else:
            suggested_process_num = max(1, recommended_max // concurrent_tasks)
            print(f"\n建议调整:")
            print(f"   方案1: 调整PROCESS_NUM={suggested_process_num}")
            print(f"   方案2: 减少并发任务数，使用顺序执行")
    else:
        print("✅ 配置合理，不会导致进程爆炸")
    
    # 性能建议
    print("\n" + "=" * 60)
    print("性能建议")
    print("=" * 60)
    
    if worker_num > cpu_count:
        print("⚠️  WORKER_NUM过大，可能导致上下文切换开销")
        print(f"   建议WORKER_NUM不超过CPU核心数: {cpu_count}")
    
    if process_num > cpu_count:
        print("⚠️  PROCESS_NUM过大，可能导致CPU竞争")
        print(f"   建议PROCESS_NUM不超过CPU核心数: {cpu_count}")
    
    print("\n" + "=" * 60)
    print("环境变量设置示例")
    print("=" * 60)
    
    if worker_num > 1:
        optimal_process_num = max(1, (recommended_max - worker_num - 1) // (worker_num * concurrent_tasks))
        print("# 多Worker环境 (生产环境)")
        print(f"export WORKER_NUM={worker_num}")
        print(f"export PROCESS_NUM={optimal_process_num}")
        print(f"export CONCURRENT_TASKS={concurrent_tasks}")
    else:
        optimal_process_num = max(1, recommended_max // concurrent_tasks)
        print("# 单Worker环境 (开发/测试环境)")
        print(f"export WORKER_NUM=1")
        print(f"export PROCESS_NUM={optimal_process_num}")
        print(f"export CONCURRENT_TASKS={concurrent_tasks}")

    print(f"\n# 保守配置 (避免进程爆炸)")
    print(f"export WORKER_NUM=1")
    print(f"export PROCESS_NUM={min(4, cpu_count)}")
    print(f"export CONCURRENT_TASKS=1  # 顺序执行，避免并发")


if __name__ == "__main__":
    check_process_configuration()
