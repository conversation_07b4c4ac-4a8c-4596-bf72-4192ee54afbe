dimension_subcategory_dim = [
    'scenarios', 'bg_code',
    'bg_name', 'oversea_code',
    'oversea_desc', 'lv1_code', 'lv1_name',
    'lv2_code', 'lv2_name',
    'currency', 'dimension_subcategory_code',
    'dimension_subcategory_cn_name',
    'dimension_subcategory_en_name',
]
dimension_group_dim = [
    'scenarios', 'bg_code',
    'bg_name', 'oversea_code', 'oversea_desc',
    'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name',
    'currency', 'dimension_group_code', 'dimension_group_cn_name', 'dimension_group_en_name'
]
lv2_dim = [
    'scenarios', 'bg_code', 'bg_name',
    'oversea_code', 'oversea_desc',
    'lv1_code',
    'lv1_name',
    'lv2_code',
    'lv2_name'
]

dimension_sub_detail_dim = [
    'scenarios', 'bg_code',
    'bg_name', 'oversea_code',
    'oversea_desc', 'lv1_code', 'lv1_name',
    'lv2_code', 'lv2_name',
    'currency', 'dimension_subcategory_code',
    'dimension_subcategory_cn_name',
    'dimension_subcategory_en_name',
]

target_tables = ["dm_fop_dimension_fcst_t", "dm_fop_dimension_lv2_fcst_t"]

dimension_lv2_fcst_group_by_cols = ['period_id', 'scenarios', 'time_window_code', 'target_period', 'bg_code', 'bg_name',
                                    'oversea_code', 'oversea_desc', 'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name',
                                    'currency', 'fcst_type']

dimension_fcst_group_by_cols = ['period_id', 'scenarios', 'time_window_code', 'target_period', 'bg_code', 'bg_name',
                                'oversea_code', 'oversea_desc', 'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name',
                                'dimension_group_code', 'dimension_group_cn_name', 'dimension_group_en_name',
                                'dimension_subcategory_code', 'dimension_subcategory_cn_name',
                                'dimension_subcategory_en_name', 'currency', 'fcst_type']

ESCAPE_SWITCH_ENABLED = "ESCAPE_SWITCH_ENABLED"
