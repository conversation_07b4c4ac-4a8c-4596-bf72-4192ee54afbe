#!/bin/sh

echo "Python Server starting........................"
binPath=$(dirname $0)
cd $binPath

mkdir /applog/logs

pip3 install --index-url https://cmc-cd-mirror.rnd.huawei.com/pypi/simple/ --extra-index-url https://cmc.centralrepo.rnd.huawei.com/artifactory/product_pypi/simple --trusted-host cmc-cd-mirror.rnd.huawei.com --trusted-host cmc.centralrepo.rnd.huawei.com -r requirements.txt
echo "pip3 install requirements done"


# 启动Web服务进程
if [ "$ENV" = "PROD-AIF" ]; then
    uvicorn main:app --host 0.0.0.0 --port 80 --workers "$WORKER_NUM" &
else
    uvicorn main:app --host 0.0.0.0 --port 8080 --workers 1 &
fi


while :
do
 sleep 1
done
