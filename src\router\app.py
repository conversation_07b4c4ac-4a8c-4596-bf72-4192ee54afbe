import asyncio
import os
import time
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from enum import Enum

from fastapi import APIRouter, Request, status
from pydantic import BaseModel

from src.expert_fusion_optimization.main_combine import combine_expert
from src.time_series_pred.common.dimension_constant import target_tables
from src.time_series_pred.db.save_to_db import soft_del_pred_version_data
from src.time_series_pred.services import nondimension_g, lv2_withdim, dimension_sub_detail, \
    dimension_sub
from src.time_series_pred.services import nondimension_sub, lv2_nodim, dimension_group
from src.utils.lts_client import lts_back
from src.utils.resource_loader import CONFIG
from src.utils.resource_loader import LOGGER as logger
from src.utils.response_generator import response_generator
from src.utils.util import current_date


# 任务类型常量
class TaskType(str, Enum):
    TIME_SERIES_PREDICTION = "时序预测"
    FUSION_OPTIMIZATION = "融合调优"


class PredTask(BaseModel):
    task_name: str
    env: str
    params: dict


router = APIRouter(
    prefix=f"{CONFIG.deploy.service_name}/v1/prediction-tasks",
    tags=["profits_service"],
)


class ProfitStatus(str, Enum):
    SUCCESS = "OK"



@router.post("/start", status_code=status.HTTP_200_OK)
async def create_task(
        request: Request,
        pred_task: PredTask
):
    # Generate a unique task ID
    lts_job_id = request.headers.get("jobId")
    pred_version = pred_task.params.get("pred_version")
    expert_data_type = pred_task.params.get("expert_data_type")
    env = pred_task.params.get("env")
    task_name = pred_task.task_name

    if task_name == TaskType.TIME_SERIES_PREDICTION:
        asyncio.create_task(time_series_prediction_task(pred_version, lts_job_id))
    elif task_name == TaskType.FUSION_OPTIMIZATION:
        asyncio.create_task(fusion_optimization_task(env, pred_version, expert_data_type, lts_job_id))

    return response_generator(
        status.HTTP_200_OK, ProfitStatus.SUCCESS, f"任务启动成功:{lts_job_id}", [{"lts_job_id": lts_job_id}])


async def time_series_prediction_task(pred_version: int, lts_job_id: str):
    """时序预测任务处理函数

    Args:
        pred_version: 预测版本
        lts_job_id: LTS任务ID
    """
    # 同一版本同时只能有一个任务在跑
    if not pred_version:
        pred_version = current_date()
    if isinstance(pred_version, str):
        pred_version = int(pred_version)
    logger.info(f"开始时序预测任务，预测版本: {pred_version}")

    try:
        await start_profits_prediction(pred_version)
        logger.info("时序任务处理结束")
        lts_back(lts_job_id, 1)
    except Exception as e:
        logger.error(f"时序预测回调通知失败: {str(e)}")
        lts_back(lts_job_id, 0)


def batch_task(method_list, pred_version):
    """批量执行预测任务

    Args:
        method_list: 预测方法列表
        pred_version: 预测版本
    """
    for pred_method in method_list:
        try:
            pred_method(pred_version=pred_version)
        except Exception as e:
            logger.error("方法{}执行失败：{}".format(pred_method, e))


async def start_profits_prediction(pred_version: int):
    """调用7个场景盈利预测

    Args:
        pred_version: 预测版本
    """
    logger.info("[START]开始执行预测任务 pred_version: {}".format(pred_version))
    if is_del_pred_version_data_enabled():
        logger.info('软删除预测版本的数据库记录')
        for target_table in target_tables:
            soft_del_pred_version_data(target_table, pred_version)

    prediction_methods = [
        dimension_sub.profit_forecast,
        dimension_group.profit_forecast,
        nondimension_sub.profit_forecast,
        nondimension_g.profit_forecast,
        lv2_nodim.profit_forecast,
        lv2_withdim.profit_forecast,
        dimension_sub_detail.profit_forecast
    ]

    max_workers = os.environ.get('THREAD_POOL_SIZE', 3)
    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        tasks = []
        for method in prediction_methods:
            task = loop.run_in_executor(executor, wrapped_prediction_method, method, pred_version)
            tasks.append(task)

        await asyncio.gather(*tasks)

    logger.info("[END]预测任务执行完成")


async def fusion_optimization_task(env: str, pred_version: int, expert_data_type: str, lts_job_id: int):
    """融合调优任务处理函数

    Args:
        env: 运行环境 (sit, uat, prod)
        pred_version: 预测版本
        expert_data_type: 专家预测数据类型
        lts_job_id: LTS任务ID
    """
    if not pred_version:
        pred_version = current_date()
    if not isinstance(pred_version, str):
        pred_version = str(pred_version)
    logger.info(f"开始融合调优任务，预测版本: {pred_version}")
    if not expert_data_type:
        expert_data_type = 'both'
    logger.info("预测以下专家类型：{}".format(expert_data_type))

    try:
        await start_fusion_optimization(env, pred_version, expert_data_type)
        logger.info("融合调优处理结束")
        lts_back(lts_job_id, 1)
    except Exception as e:
        logger.error(f"融合调优回调通知失败: {str(e)}")
        lts_back(lts_job_id, 0)


async def start_fusion_optimization(env: str, pred_version: str, expert_data_type: str):
    """调用专家融合调优

    Args:
        env: 运行环境 (sit, uat, prod)
        pred_version: 当前会计期 (str)
        expert_data_type: 专家预测的数据类型 (group, chanye, both)
    """
    logger.info("[START]开始执行融合调优任务")
    await asyncio.to_thread(wrapped_prediction_method, combine_expert, env, pred_version, expert_data_type)
    logger.info("[END]融合调优任务执行完成")


def is_del_pred_version_data_enabled() -> bool:
    """检查是否启用预测版本数据删除功能
    """
    return os.environ.get('DEL_PRED_VERSION_ENABLED', 'false').lower() == 'true'


def wrapped_prediction_method(method, *args, **kwargs):
    """用于日志记录和异常处理，支持任意参数。
    """
    method_name = method.__qualname__
    logger.info(f"[{method_name}] 开始执行，参数: args={args}, kwargs={kwargs}")

    start_time = time.time()
    try:
        result = method(*args, **kwargs)
        execution_time = time.time() - start_time
        logger.info(f"[{method_name}] 执行成功，耗时: {execution_time:.2f}s")
        return result
    except Exception as e:
        logger.error(f"[{method_name}] 执行失败: {e}", exc_info=True)
        raise