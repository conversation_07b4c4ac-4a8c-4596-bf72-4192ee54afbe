#!/usr/bin/env python3
"""
运行pytest测试的脚本
"""
import subprocess
import sys
import os

def run_tests():
    """运行所有测试"""
    print("开始运行pytest测试...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = os.path.dirname(os.path.abspath(__file__))
    
    try:
        # 运行pytest
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            'tests/', 
            '-v',
            '--tb=short'
        ], env=env, capture_output=True, text=True)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 所有测试通过!")
        else:
            print("❌ 测试失败!")
            
        return result.returncode
        
    except Exception as e:
        print(f"运行测试时出错: {e}")
        return 1

if __name__ == '__main__':
    exit_code = run_tests()
    sys.exit(exit_code)
