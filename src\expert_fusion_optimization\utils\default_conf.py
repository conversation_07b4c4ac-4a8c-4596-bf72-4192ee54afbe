# 数据库表名
AI_FCST_TBL_NAME = "dm_fop_dimension_lv1_lv2_aggr_fcst_t"                      # AI预测结果表
GROUP_EXPERT_FCST_TBL_NAME = "kr_cpf_lv1_group_analysts_fcst_t"  # 集团专家预测结果表
DIM_EXPERT_FCST_TBL_NAME = "dm_fop_dimension_group_analysts_result_t"  # 量纲分析师预测结果表
CHANYE_EXPERT_FCST_TBL_NAME = "dm_fop_dimension_industry_analysts_fcst_t"           # 产业专家预测结果表
FACT_TBL_NAME = "dm_fop_dimension_lv2_tgt_period_t"                          # 真实数据表
COMBINED_TBL_NAME = "dm_fop_dimension_lv2_aggr_fcst_comb_t"                # 融合结果表
VERSION_TBL_NAME = "dm_fop_dimension_version_t"                #取版本号的表名

# 数据库表字段名
MGP_RATE_FIELD = "mgp_ratio"                      # 分析师预测结果表制毛率字段名
EQUIP_REV_AMT_FIELD = "equip_rev_amt"             # 分析师预测结果表设备收入字段名
AI_MGP_RATE_FIELD = "mgp_rate_after_fcst"         # AI预测结果表制毛率字段名
AI_EQUIP_REV_AMT_FIELD = "equip_rev_after_fcst"   # AI预测结果表设备收入字段名
FACT_MGP_RATE_FIELD = "mgp_ratio_after"        # 事实表制毛率字段名
FACT_EQUIP_REV_AMT_FIELD = "equip_rev_cons_after_amt"  # 事实表设备收入字段名

# AI预测需要处理的预测方法
FCST_TYPE_LIST = ["YTD法"]

# 专家预测类型
GROUP_EXPERT_TYPE = "group"      # 集团分析师类型
GROUP_EXPERT_MARK = "集团分析师"   # 数据库中专家类型标记
DIM_EXPERT_TYPE = "DIM"      # 量纲分析师类型
DIM_EXPERT_MARK = "量纲分析师"   # 数据库中专家类型标记
CHANYE_EXPERT_TYPE = "chanye"    # 产业分析师类型
CHANYE_EXPERT_MARK = "产业分析师"  # 数据库中专家类型标记

# S3数据存储路径
COMBINED_MIDDLE_RESULT_PATH = "combine_data"    # AI预测融合专家结果的中间数据存储路径
COMBINED_RESULT_PATH = "combine_data"           # AI预测融合专家结果的存储路径

# 集团盈利预测任务名称
COMBINE_EXPERT_TASK = "combine_expert"  # 融合专家预测结果任务

# 空字符串占位符
NULL_STRING_PLACE_HOLDER = "SNULL"
